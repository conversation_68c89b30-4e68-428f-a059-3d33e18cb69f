'''Implement Bayesian optimization using bayes_opt.'''

from dataclasses import dataclass
from typing import Any, Callable, Literal, Sequence, Type

import numpy as np
from numpy.typing import NDArray
from scipy.optimize import NonlinearConstraint

from optimagic import mark
from optimagic.config import IS_BAYESOPT_INSTALLED
from optimagic.exceptions import NotInstalledError
from optimagic.optimization.algo_options import N_RESTARTS
from optimagic.optimization.algorithm import Algorithm, InternalOptimizeResult
from optimagic.optimization.internal_optimization_problem import (
    InternalBounds,
    InternalOptimizationProblem,
)
from optimagic.typing import (
    AggregationLevel,
    NonNegativeFloat,
    NonNegativeInt,
    PositiveFloat,
    PositiveInt,
)

if IS_BAYESOPT_INSTALLED:
    from bayes_opt import BayesianOptimization, acquisition
    from bayes_opt.acquisition import AcquisitionFunction


@mark.minimizer(
    name="bayes_opt",
    solver_type=AggregationLevel.SCALAR,
    is_available=IS_BAYESOPT_INSTALLED,
    is_global=True,
    needs_jac=False,
    needs_hess=False,
    supports_parallelism=False,
    supports_bounds=True,
    supports_linear_constraints=False,
    supports_nonlinear_constraints=False,  # temp
    disable_history=False,
)
@dataclass(frozen=True)
class BayesOpt(Algorithm):
    init_points: PositiveInt = 5
    n_iter: PositiveInt = 25
    verbose: Literal[0, 1, 2] = 0
    kappa: NonNegativeFloat = 2.576
    xi: PositiveFloat = 0.01
    exploration_decay: float | None = None
    exploration_decay_delay: NonNegativeInt | None = None
    random_state: int | None = None
    acquisition_function: (
        str | AcquisitionFunction | Type[AcquisitionFunction] | None
    ) = None
    cl_base_acquisition: str | AcquisitionFunction | None = "ei"
    cl_strategy: Literal["min", "mean", "max"] | float = "max"
    cl_atol: PositiveFloat = 1e-5
    cl_rtol: PositiveFloat = 1e-8
    gph_acquisitions: list[str | AcquisitionFunction] | None = None
    allow_duplicate_points: bool = False
    enable_sdr: bool = False
    sdr_gamma_osc: float = 0.7
    sdr_gamma_pan: float = 1.0
    sdr_eta: float = 0.9
    sdr_minimum_window: NonNegativeFloat = 0.0
    alpha: float = 1e-6
    n_restarts: int = N_RESTARTS

    def _solve_internal_problem(
        self, problem: InternalOptimizationProblem, x0: NDArray[np.float64]
    ) -> InternalOptimizeResult:
        if not IS_BAYESOPT_INSTALLED:
            raise NotInstalledError(
                "To use the 'bayes_opt' optimizer you need to install bayes_opt. "
                "Use 'pip install bayesian-optimization'. "
                "Check the documentation for more details: "
                "https://bayesian-optimization.github.io/BayesianOptimization/index.html"
            )

        pbounds = _process_bounds(problem.bounds)

        acq = _create_acquisition_function(
            acquisition_function=self.acquisition_function,
            kappa=self.kappa,
            xi=self.xi,
            exploration_decay=self.exploration_decay,
            exploration_decay_delay=self.exploration_decay_delay,
            random_state=self.random_state,
            cl_base_acquisition=self.cl_base_acquisition,
            cl_strategy=self.cl_strategy,
            cl_atol=self.cl_atol,
            cl_rtol=self.cl_rtol,
            gph_acquisitions=self.gph_acquisitions,
        )

        constraint = None
        constraint = self._process_constraints(problem.nonlinear_constraints)

        def objective(**kwargs: dict[str, float]) -> float:
            x = _extract_params_from_kwargs(kwargs, len(x0))
            return -float(
                problem.fun(x)
            )  # Negate to convert minimization to maximization

        bounds_transformer = None
        if self.enable_sdr:
            from bayes_opt import SequentialDomainReductionTransformer

            bounds_transformer = SequentialDomainReductionTransformer(
                gamma_osc=self.sdr_gamma_osc,
                gamma_pan=self.sdr_gamma_pan,
                eta=self.sdr_eta,
                minimum_window=self.sdr_minimum_window,
            )

        optimizer = BayesianOptimization(
            f=objective,
            pbounds=pbounds,
            acquisition_function=acq,
            constraint=constraint,
            random_state=self.random_state,
            verbose=self.verbose,
            bounds_transformer=bounds_transformer,
            allow_duplicate_points=self.allow_duplicate_points,
        )

        # Set Gaussian Process parameters
        optimizer.set_gp_params(alpha=self.alpha, n_restarts_optimizer=self.n_restarts)

        # Use initial point as first probe
        probe_params = {f"param{i}": float(val) for i, val in enumerate(x0)}
        optimizer.probe(
            params=probe_params,
            lazy=True,
        )
        optimizer.maximize(
            init_points=self.init_points,
            n_iter=self.n_iter,
        )

        res = _process_bayes_opt_result(optimizer=optimizer, x0=x0, problem=problem)
        return res

    def _process_constraints(
        self, constraints: list[dict[str, Any]] | None
    ) -> NonlinearConstraint | None:
        '''Temporarily skip processing of nonlinear constraints.

        Args:
            constraints: List of constraint dictionaries from the problem

        Returns:
            None. Nonlinear constraint processing is deferred.

        '''
        # TODO: Implement proper handling of nonlinear constraints in future.
        return None


def _process_bounds(bounds: InternalBounds) -> dict[str, tuple[float, float]]:
    '''Process bounds for bayesian optimization.

    Args:
        bounds: Internal bounds object.

    Returns:
        Dictionary mapping parameter names to (lower, upper) bound tuples.

    Raises:
        ValueError: If bounds are None or infinite.

    '''
    if not (
        bounds.lower is not None
        and bounds.upper is not None
        and np.all(np.isfinite(bounds.lower))
        and np.all(np.isfinite(bounds.upper))
    ):
        raise ValueError(
            "Bayesian optimization requires finite bounds for all parameters. "
            "Bounds cannot be None or infinite."
        )

    return {
        f"param{i}": (lower, upper)
        for i, (lower, upper) in enumerate(zip(bounds.lower, bounds.upper, strict=True))
    }


def _extract_params_from_kwargs(
    kwargs: dict[str, Any], n_params: int
) -> NDArray[np.float64]:
    '''Extract parameters from kwargs dictionary.

    Args:
        kwargs: Dictionary with parameter values.
        n_params: Number of parameters to extract.

    Returns:
        Array of parameter values.

    '''
    return np.array([kwargs[f"param{i}"] for i in range(n_params)])


def _create_base_acquisition_function(
    acq_spec: str | AcquisitionFunction,
    kappa: NonNegativeFloat,
    xi: PositiveFloat,
    exploration_decay: float | None,
    exploration_decay_delay: NonNegativeInt | None,
    random_state: int | None,
) -> AcquisitionFunction:
    '''Create a base acquisition function (ucb, ei, poi).'''
    if isinstance(acq_spec, AcquisitionFunction):
        return acq_spec

    if not isinstance(acq_spec, str):
        raise TypeError(
            "Base acquisition function specification must be a string or an "
            "AcquisitionFunction instance."
        )

    acq_name = acq_spec.lower()
    base_aliases = {
        "ucb": "ucb",
        "upper_confidence_bound": "ucb",
        "ei": "ei",
        "expected_improvement": "ei",
        "poi": "poi",
        "probability_of_improvement": "poi",
    }
    canonical_name = base_aliases.get(acq_name)

    if canonical_name == "ucb":
        return acquisition.UpperConfidenceBound(
            kappa=kappa,
            exploration_decay=exploration_decay,
            exploration_decay_delay=exploration_decay_delay,
            random_state=random_state,
        )
    elif canonical_name == "ei":
        return acquisition.ExpectedImprovement(
            xi=xi,
            exploration_decay=exploration_decay,
            exploration_decay_delay=exploration_decay_delay,
            random_state=random_state,
        )
    elif canonical_name == "poi":
        return acquisition.ProbabilityOfImprovement(
            xi=xi,
            exploration_decay=exploration_decay,
            exploration_decay_delay=exploration_decay_delay,
            random_state=random_state,
        )
    else:
        raise ValueError(
            f"Invalid base acquisition function string: '{acq_spec}'. "
            f"Must be one of: {', '.join(base_aliases.keys())}"
        )


def _create_acquisition_function(
    acquisition_function: str | AcquisitionFunction | Type[AcquisitionFunction] | None,
    kappa: NonNegativeFloat,
    xi: PositiveFloat,
    exploration_decay: float | None,
    exploration_decay_delay: NonNegativeInt | None,
    random_state: int | None,
    cl_base_acquisition: str | AcquisitionFunction | None,
    cl_strategy: Literal["min", "mean", "max"] | float,
    cl_atol: PositiveFloat,
    cl_rtol: PositiveFloat,
    gph_acquisitions: list[str | AcquisitionFunction] | None,
) -> AcquisitionFunction | None:
    '''Create and return the appropriate acquisition function.'''
    if acquisition_function is None:
        return None

    if isinstance(acquisition_function, AcquisitionFunction):
        return acquisition_function

    if isinstance(acquisition_function, type) and issubclass(
        acquisition_function, acquisition.AcquisitionFunction
    ):
        return acquisition_function()

    if not isinstance(acquisition_function, str):
        raise TypeError(
            "acquisition_function must be None, a string, an AcquisitionFunction "
            f"instance, or a class. Got {type(acquisition_function).__name__}."
        )

    acq_name = acquisition_function.lower()
    all_aliases = {
        "ucb": "ucb",
        "upper_confidence_bound": "ucb",
        "ei": "ei",
        "expected_improvement": "ei",
        "poi": "poi",
        "probability_of_improvement": "poi",
        "constant_liar": "cl",
        "cl": "cl",
        "gp_hedge": "gph",
        "gph": "gph",
    }
    canonical_name = all_aliases.get(acq_name)

    if canonical_name in ("ucb", "ei", "poi"):
        return _create_base_acquisition_function(
            canonical_name, kappa, xi, exploration_decay, exploration_decay_delay, random_state
        )

    elif canonical_name == "cl":
        base_acq = _create_base_acquisition_function(
            cl_base_acquisition, kappa, xi, exploration_decay, exploration_decay_delay, random_state
        )
        return acquisition.ConstantLiar(
            base_acquisition=base_acq,
            strategy=cl_strategy,
            random_state=random_state,
            atol=cl_atol,
            rtol=cl_rtol,
        )

    elif canonical_name == "gph":
        if gph_acquisitions is None:
            raise ValueError("gph_acquisitions must be specified for GP Hedge.")
        base_acqs = [
            _create_base_acquisition_function(
                spec, kappa, xi, exploration_decay, exploration_decay_delay, random_state
            )
            for spec in gph_acquisitions
        ]
        return acquisition.GPHedge(
            base_acquisitions=base_acqs,
            random_state=random_state,
        )

    else:
        raise ValueError(f"Invalid acquisition_function string: '{acquisition_function}'")


def _process_bayes_opt_result(
    optimizer: "BayesianOptimization",
    x0: NDArray[np.float64],
    problem: InternalOptimizationProblem,
) -> InternalOptimizeResult:
    '''Convert BayesianOptimization result to InternalOptimizeResult format.

    Args:
        optimizer: The BayesianOptimization instance after optimization
        x0: Initial parameter values
        problem: The internal optimization problem

    Returns:
        InternalOptimizeResult with processed results

    '''
    n_evals = len(optimizer.space)

    if optimizer.max is not None:
        best_params = optimizer.max["params"]
        best_x = _extract_params_from_kwargs(best_params, len(x0))
        best_y = -optimizer.max["target"]  # Un-negate the result
        success = True
        message = "Optimization succeeded"
    else:
        best_x = x0
        best_y = float(problem.fun(x0))
        success = False
        message = (
            "Optimization did not succeed "
            "returning the initial point as the best available result."
        )

    return InternalOptimizeResult(
        x=best_x,
        fun=best_y,
        success=success,
        message=message,
        n_iterations=n_evals,
        n_fun_evals=n_evals,
        n_jac_evals=0,
    )