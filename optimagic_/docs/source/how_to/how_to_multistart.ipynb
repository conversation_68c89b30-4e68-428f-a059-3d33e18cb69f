{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["(how-to-multistart)=\n", "\n", "# How to do multistart optimizations\n", "\n", "Sometimes you want to make sure that your optimization is robust to the initial\n", "parameter values, i.e. that it does not get stuck at a local optimum. This is where\n", "multistart comes in handy.\n", "\n", "\n", "## What does multistart (not) do\n", "\n", "In short, multistart iteratively runs local optimizations from different initial\n", "conditions. If enough local optimization convergence to the same point, it stops.\n", "Importantly, it cannot guarantee that the result is the global optimum, but it can\n", "increase your confidence in the result.\n", "\n", "## TL;DR\n", "\n", "To activate multistart at the default options, pass `multistart=True` to the `minimize`\n", "or `maximize` function, as well as finite bounds on the parameters (which are used to\n", "sample the initial points). The default options are discussed below."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "import optimagic as om\n", "\n", "\n", "def fun(x):\n", "    return x @ x\n", "\n", "\n", "x0 = np.arange(7) - 4\n", "\n", "bounds = om.Bounds(\n", "    lower=np.full_like(x0, -5),\n", "    upper=np.full_like(x0, 10),\n", ")\n", "\n", "algo_options = {\"stopping_maxfun\": 1_000}\n", "\n", "res = om.minimize(\n", "    fun=fun,\n", "    x0=x0,\n", "    algorithm=\"scipy_neldermead\",\n", "    algo_options=algo_options,\n", "    bounds=bounds,\n", "    multistart=True,\n", ")"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["In this example, we limited each local optimization to 1_000 function evaluations. In\n", "general, it is a good idea to limit the number of iterations and function evaluations\n", "for the local optimization. Because of the iterative nature of multistart, this\n", "limitation will usually not result in a precision issue."]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## What does multistart mean in optimagic?\n", "\n", "Our multistart optimizations are inspired by the [TikTak algorithm](https://github.com/serdarozkan/TikTak) and consist of the following steps:\n", "\n", "1. Draw a large exploration sample of parameter vectors randomly or using a\n", "   low-discrepancy sequence.\n", "1. Evaluate the objective function in parallel on the exploration sample.\n", "1. Sort the parameter vectors from best to worst according to their objective function\n", "   values. \n", "1. Run local optimizations iteratively. That is, the first local optimization is started\n", "   from the best parameter vector in the sample. All subsequent ones are started from a\n", "   convex combination of the currently best known parameter vector and the next sample\n", "   point. "]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["## Visualizing multistart results\n", "\n", "To illustrate the multistart results, we will consider the optimization of a slightly\n", "more complex objective function, compared to `fun` from above. We also limit the\n", "number of exploration samples to 100."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["def alpine(x):\n", "    return np.sum(np.abs(x * np.sin(x) + 0.1 * x))\n", "\n", "\n", "res = om.minimize(\n", "    alpine,\n", "    x0=x0,\n", "    algorithm=\"scipy_neldermead\",\n", "    bounds=bounds,\n", "    algo_options=algo_options,\n", "    multistart=om.MultistartOptions(n_samples=100, seed=0),\n", ")\n", "\n", "fig = om.criterion_plot(res, monotone=True)\n", "fig.show(\"png\")"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["In the above image we see the optimization history for all of the local optimizations\n", "that have been run by multistart. The turquoise line represents the history\n", "corresponding to the local optimization that found the overall best parameter.\n", "\n", "We see that running a single optimization would not have sufficed, as some local\n", "optimizations are stuck."]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["## Multistart does not always run many optimization\n", "\n", "Since the local optimizations are run iteratively by multistart, it is possible that\n", "only a handful of optimizations are actually run if all of them converge to the same\n", "point. This convergence is determined by the `convergence_max_discoveries` option,\n", "which defaults to 2. This means that if 2 local optimizations report the same point,\n", "multistart will stop. Below we see that if we use the simpler objective function\n", "(`fun`), and the `scipy_lbfgsb` algorithm, multistart runs only 2 local optimizations,\n", "and then stops, as both of them converge to the same point. Note that, the\n", "`scipy_lbfgsb` algorithm can solve this simple problem precisely, without reaching the\n", "maximum number of function evaluations."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun,\n", "    x0=x0,\n", "    algorithm=\"scipy_lbfgsb\",\n", "    bounds=bounds,\n", "    algo_options=algo_options,\n", "    multistart=om.MultistartOptions(n_samples=100, seed=0),\n", ")\n", "\n", "fig = om.criterion_plot(res)\n", "fig.show(\"png\")"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["## How to configure multistart\n", "\n", "Configuration of multistart can be done by passing an instance of\n", "`optimagic.MultistartOptions` to `minimize` or `maximize`. Let's look at a few examples\n", "configurations."]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["### How to run a specific number of optimizations\n", "\n", "To run a specific number of local optimizations, you need to set the `stopping_maxopt`\n", "option. Note that this does not set the number of exploration samples, which is\n", "controlled by the `n_samples` option. The number of exploration samples always needs\n", "to be at least as large as the number of local optimizations.\n", "\n", "Note that, as long as `convergence_max_discoveries` is smaller than `stopping_maxopt`,\n", "it is possible that a smaller number of local optimizations are run. To avoid this,\n", "set `convergence_max_discoveries` to a value at least as large as `stopping_maxopt`.\n", "\n", "To run, for example, 10 local optimizations from 15 exploration samples, do:"]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    alpine,\n", "    x0=x0,\n", "    algorithm=\"scipy_neldermead\",\n", "    bounds=bounds,\n", "    algo_options=algo_options,\n", "    multistart=om.MultistartOptions(\n", "        n_samples=15,\n", "        stopping_maxopt=10,\n", "        convergence_max_discoveries=10,\n", "    ),\n", ")\n", "\n", "res.multistart_info.n_optimizations"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["### How to set a custom exploration sample\n", "\n", "If you want to start the multistart algorithm with a custom exploration sample, you can\n", "do so by passing a sequence of parameters to the `sample` option. Note that sequence\n", "elements must be of the same type as your parameter.\n", "\n", "To generate a sample of 100 random parameters and run them through the multistart\n", "algorithm, do:"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["rng = np.random.default_rng(12345)\n", "\n", "sample = [x0 + rng.uniform(-1, 1, size=len(x0)) for _ in range(100)]\n", "\n", "res = om.minimize(\n", "    alpine,\n", "    x0=x0,\n", "    algorithm=\"scipy_neldermead\",\n", "    bounds=bounds,\n", "    algo_options=algo_options,\n", "    multistart=om.MultistartOptions(sample=sample),\n", ")"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["### How to run multistart in parallel\n", "\n", "\n", "The multistart algorithm can be run in parallel by setting the `n_cores` option to a\n", "value greater than 1. This will run the algorithm in batches. By default, the batch\n", "size is set to `n_cores`, but can be controlled by setting the `batch_size` option. The\n", "default batch evaluator is `joblib`, but can be controlled by setting the\n", "`batch_evaluator` option to `\"pathos\"` or a custom callable.\n", "\n", "To run the multistart algorithm in parallel, do:"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    alpine,\n", "    x0=x0,\n", "    algorithm=\"scipy_lbfgsb\",\n", "    bounds=bounds,\n", "    algo_options=algo_options,\n", "    multistart=om.MultistartOptions(n_cores=2),\n", ")"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["## What to do if you do not have bounds\n", "\n", "Multistart requires finite bounds on the parameters. If your optimization problem is not\n", "bounded, you can set soft lower and upper bounds. These bounds will only be used to\n", "draw the exploration sample, and will not be used to constrain the local optimizations.\n", "\n", "To set soft bounds, do:"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    alpine,\n", "    x0=x0,\n", "    algorithm=\"scipy_lbfgsb\",\n", "    bounds=om.Bounds(soft_lower=np.full_like(x0, -3), soft_upper=np.full_like(x0, 8)),\n", "    multistart=True,\n", ")"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["## Understanding multistart results\n", "\n", "When activating multistart, the optimization result object corresponds to the local\n", "optimization that found the best objective function value. The result object has the\n", "additional attribute `multistart_info`, where all of the additional information is\n", "stored. It has the following attributes:\n", "\n", "- `local_optima`: A list with the results from all local optimizations that were performed.\n", "- `start_parameters`: A list with the start parameters from those optimizations \n", "- `exploration_sample`: A list with parameter vectors at which the objective function was evaluated in an initial exploration phase. \n", "- `exploration_results`: The corresponding objective values.\n", "- `n_optimizations`: The number of local optimizations that were run.\n", "\n", "To illustrate the multistart results, let us consider the optimization of the simple\n", "`fun` objective function from above."]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun,\n", "    x0=x0,\n", "    algorithm=\"scipy_lbfgsb\",\n", "    bounds=bounds,\n", "    algo_options=algo_options,\n", "    multistart=om.MultistartOptions(n_samples=100, convergence_max_discoveries=2),\n", ")"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["### Start parameters\n", "\n", "The start parameters are the parameter vectors from which the local optimizations were\n", "started. Since the default number of `convergence_max_discoveries` is 2, and both\n", "local optimizations were successfull, the start parameters have 2 rows."]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["res.multistart_info.start_parameters"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["### Local Optima\n", "\n", "The local optima are the results from the local optimizations. Since in this example\n", "only two local optimizations were run, the local optima list has two elements, each of\n", "which is an optimization result object."]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["len(res.multistart_info.local_optima)"]}, {"cell_type": "markdown", "id": "24", "metadata": {}, "source": ["### Exploration sample\n", "\n", "The exploration sample is a list of parameter vectors at which the objective function\n", "was evaluated. Above, we chose a random exploration sample of 100 parameter vectors."]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["np.vstack(res.multistart_info.exploration_sample).shape"]}, {"cell_type": "markdown", "id": "26", "metadata": {}, "source": ["### Exploration results\n", "\n", "The exploration results are the objective function values at the exploration sample."]}, {"cell_type": "code", "execution_count": null, "id": "27", "metadata": {}, "outputs": [], "source": ["len(res.multistart_info.exploration_results)"]}, {"cell_type": "markdown", "id": "28", "metadata": {}, "source": ["### Number of local optimizations"]}, {"cell_type": "code", "execution_count": null, "id": "29", "metadata": {}, "outputs": [], "source": ["res.multistart_info.n_optimizations"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}