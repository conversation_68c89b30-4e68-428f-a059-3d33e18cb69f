[project]
name = "bayesian-optimization"
version = "3.0.0"
description = "Bayesian Optimization package"
authors = [{ name = "<PERSON>", email = "fmf<PERSON><PERSON><PERSON>@gmail.com" }]
license = { file = "LICENSE" }
readme = "README.md"
requires-python = ">=3.9,<4.0"
classifiers = [
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
]
dependencies = [
    "scikit-learn>=1.0.0,<2.0.0",
    "numpy>=1.25; python_version<'3.13'",
    "numpy>=2.1.3; python_version>='3.13'",
    "scipy>=1.0.0,<2.0.0; python_version<'3.13'",
    "scipy>=1.14.1,<2.0.0; python_version>='3.13'",
    "colorama>=0.4.6,<1.0.0",
]

[tool.poetry]
requires-poetry = ">=2.0"
packages = [{ include = "bayes_opt" }]


[tool.poetry.group.dev] # for testing/developing
optional = true
[tool.poetry.group.dev.dependencies]
pytest = "^8.0.0"
pytest-cov = "^4.1.0"
coverage = "^7.4.1"
ruff = "0.6.6"
pre-commit = "^3.7.1"


[tool.poetry.group.nbtools] # for running/converting notebooks
optional = true
[tool.poetry.group.nbtools.dependencies]
nbformat = "^5.9.2"
nbconvert = "^7.14.2"
jupyter = "^1.0.0"
matplotlib = "^3.0"
nbsphinx = "^0.9.4"
sphinx-immaterial = "^0.12.0"
sphinx = [
    { version = "^7.0.0", python = "<3.10" },
    { version = "^8.0.0", python = ">=3.10" },
]
sphinx-autodoc-typehints = [
    { version = "^2.3.0", python = "<3.10" },
    { version = "^2.4.0", python = ">=3.10" },
]


[build-system]
requires = ["poetry-core>=2.0"]
build-backend = "poetry.core.masonry.api"

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "if TYPE_CHECKING:",
]
