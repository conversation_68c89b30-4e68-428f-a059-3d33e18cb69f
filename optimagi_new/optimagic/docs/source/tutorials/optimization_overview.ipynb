{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Numerical optimization\n", "\n", "Using simple examples, this tutorial shows how to do an optimization with optimagic. More details on the topics covered here can be found in the [how to guides](../how_to/index.md)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "import optimagic as om"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic usage of `minimize`\n", "\n", "The basic usage of `optimagic.minimize` is very similar to `scipy.optimize.minimize`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sphere(params):\n", "    return params @ params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lbfgsb_res = om.minimize(\n", "    fun=sphere,\n", "    params=np.arange(5),\n", "    algorithm=\"scipy_lbfgsb\",\n", ")\n", "\n", "lbfgsb_res.params.round(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## `params` do not have to be vectors\n", "\n", "In optimagic, params can by arbitrary [pytrees](https://jax.readthedocs.io/en/latest/pytrees.html). Examples are (nested) dictionaries of numbers, arrays and pandas objects. This is very useful if you have many parameters!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def dict_sphere(params):\n", "    return params[\"a\"] ** 2 + params[\"b\"] ** 2 + (params[\"c\"] ** 2).sum()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["nm_res = om.minimize(\n", "    fun=dict_sphere,\n", "    params={\"a\": 0, \"b\": 1, \"c\": pd.Series([2, 3, 4])},\n", "    algorithm=\"scipy_neldermead\",\n", ")\n", "\n", "nm_res.params"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## You can compare optimizers\n", "\n", "In practice, it is super hard to pick the right optimizer for your problem. With optimagic, you can simply try a few and compare their results!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = {\"lbfgsb\": lbfgsb_res, \"nelder_mead\": nm_res}\n", "fig = om.criterion_plot(results, max_evaluations=300)\n", "fig.show(renderer=\"png\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can also zoom in on the history of specific parameters. This can be super helpful to diagnose problems in the optimization. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig = om.params_plot(\n", "    nm_res,\n", "    max_evaluations=300,\n", "    # optionally select a subset of parameters to plot\n", "    selector=lambda params: params[\"c\"],\n", ")\n", "fig.show(renderer=\"png\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## There are many optimizers\n", "\n", "By default, optimagic comes with optimizers from scipy, including global optimizers \n", "and least-squares optimizers. But we also have wrappers for algorithms from **NlOpt**, \n", "**Pygmo**, as well as several optimizers from individual packages like **fides**, \n", "**ipopt**, **pybobyqa** and **dfols**. \n", "\n", "To use optimizers that are not from scipy, follow our [installation guide](../installation.md) for optional dependencies. To see which optimizers we have, check out the [full list](../algorithms.md).\n", "\n", "If you are missing your favorite optimizer in the list, let us know with an [issue](https://github.com/optimagic-dev/optimagic/issues)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Amazing autocomplete \n", "\n", "Assume you need a gradient-free optimizer that supports bounds on the parameters. Moreover, you have a fixed computational budget, so you want to set stopping options. \n", "\n", "In most optimizer libraries, you would have to spend a few minutes with the docs to find an optimizer that fits your needs and the stopping options it supports. In optimagic, all of this is discoverable in your editor!\n", "\n", "If you type `om.algos.`, your editor will show you all available optimizers and a list of categories you can use to filter the results. In our case, we select `GradientFree` and `Bounded`, and we could do that in any order we want.\n", "\n", "\n", "![autocomplete_1](../_static/images/autocomplete_1.png)\n", "\n", "\n", "After selecting one of the displayed algorithms, in our case `scipy_neldermead`, the editor shows all tuning parameters of that optimizer. If you start to type `stopping`, you will see all stopping criteria that are available.\n", "\n", "\n", "![autocomplete_2](../_static/images/autocomplete_2.png)\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Adding bounds\n", "\n", "As any optimizer library, optimagic lets you specify bounds for the parameters."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bounds = om.Bounds(lower=np.arange(5) - 2, upper=np.array([10, 10, 10, np.inf, np.inf]))\n", "\n", "res = om.minimize(\n", "    fun=sphere,\n", "    params=np.arange(5),\n", "    algorithm=\"scipy_lbfgsb\",\n", "    bounds=bounds,\n", ")\n", "\n", "res.params.round(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Fixing parameters \n", "\n", "On top of bounds, you can also fix one or more parameters during the optimization. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun=sphere,\n", "    params=np.arange(5),\n", "    algorithm=\"scipy_lbfgsb\",\n", "    constraints=om.FixedConstraint(selector=lambda params: params[[1, 3]]),\n", ")\n", "\n", "res.params.round(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Other constraints\n", "\n", "As an example, let's impose the constraint that the first three parameters are valid probabilities, i.e. they are between zero and one and sum to one:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun=sphere,\n", "    params=np.array([0.1, 0.5, 0.4, 4, 5]),\n", "    algorithm=\"scipy_lbfgsb\",\n", "    constraints=om.ProbabilityConstraint(selector=lambda params: params[:3]),\n", ")\n", "\n", "res.params.round(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For a full overview of the constraints we support and the corresponding syntaxes, check out [the documentation](../how_to/how_to_constraints.md).\n", "\n", "Note that `\"scipy_lbfgsb\"` is not a constrained optimizer. If you want to know how we achieve this, check out [the explanations](../explanation/implementation_of_constraints.md)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## There is also maximize\n", "\n", "If you ever forgot to switch back the sign of your criterion function after doing a maximization with `scipy.optimize.minimize`, there is good news:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def upside_down_sphere(params):\n", "    return -params @ params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res = om.maximize(\n", "    fun=upside_down_sphere,\n", "    params=np.arange(5),\n", "    algorithm=\"scipy_bfgs\",\n", ")\n", "\n", "res.params.round(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["optimagic got your back."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Speeding up your optimization with derivatives \n", "\n", "You can speed up your optimization by providing closed form derivatives. Those derivatives can be hand-coded or calculated with JAX!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sphere_gradient(params):\n", "    return 2 * params\n", "\n", "\n", "res = om.minimize(\n", "    fun=sphere,\n", "    params=np.arange(5),\n", "    algorithm=\"scipy_lbfgsb\",\n", "    jac=sphere_gradient,\n", ")\n", "res.params.round(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, you can let optimagic calculate numerical derivatives with parallelized finite differences. This is very handy if you do not want to invest the time to derive the derivatives of your criterion function. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun=sphere,\n", "    params=np.arange(5),\n", "    algorithm=\"scipy_lbfgsb\",\n", "    numdiff_options=om.NumdiffOptions(n_cores=6),\n", ")\n", "\n", "res.params.round(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For more details and examples check out [how-to speed up your optimization with derivatives](../how_to/how_to_derivatives.ipynb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Turn local optimizers global with multistart\n", "\n", "Multistart optimization requires finite soft bounds on all parameters. Those bounds will\n", "be used for sampling but not enforced during optimization."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bounds = om.Bounds(soft_lower=np.full(10, -5), soft_upper=np.full(10, 15))\n", "\n", "res = om.minimize(\n", "    fun=sphere,\n", "    params=np.arange(10),\n", "    algorithm=\"scipy_neldermead\",\n", "    bounds=bounds,\n", "    multistart=om.MultistartOptions(convergence_max_discoveries=5),\n", ")\n", "res.params.round(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## And plot the history of all local optimizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig = om.criterion_plot(res)\n", "fig.show(renderer=\"png\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exploit the structure of your optimization problem\n", "\n", "Many estimation problems have a least-squares structure. If so, specialized optimizers that exploit this structure can be much faster than standard optimizers. The `sphere` function from above is the simplest possible least-squarse problem you could imagine: the least-squares residuals are just the params. \n", "\n", "To use least-squares optimizers in optimagic, you need to declare mark your function with \n", "a decorator and return the least-squares residuals instead of the aggregated function value. \n", "\n", "More details can be found [here](../how_to/how_to_criterion_function.md)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@om.mark.least_squares\n", "def ls_sphere(params):\n", "    return params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun=ls_sphere,\n", "    params=np.arange(5),\n", "    algorithm=\"pounders\",\n", ")\n", "res.params.round(5)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Of course, any least-squares problem can also be solved with a standard optimizer. \n", "\n", "There are also specialized optimizers for likelihood functions. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using and reading persistent logging\n", "\n", "For long-running and difficult optimizations, it can be worthwhile to store the progress in a persistent log file. You can do this by providing a path to the `logging` argument:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun=sphere,\n", "    params=np.arange(5),\n", "    algorithm=\"scipy_lbfgsb\",\n", "    logging=\"my_log.db\",\n", "    log_options={\"if_database_exists\": \"replace\"},\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can read the entries in the log file (while the optimization is still running or after it has finished) as follows:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reader = om.OptimizeLogReader(\"my_log.db\")\n", "reader.read_history().keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For more information on what you can do with the log file and LogReader object, check out [the logging tutorial](../how_to/how_to_logging.ipynb)\n", "\n", "The persistent log file is always instantly synchronized when the optimizer tries a new parameter vector. This is very handy if an optimization has to be aborted and you want to extract the current status. It can be displayed in `criterion_plot` and `params_plot`, even while the optimization is running. "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}, "vscode": {"interpreter": {"hash": "40d3a090f54c6569ab1632332b64b2c03c39dcf918b08424e98f38b5ae0af88f"}}}, "nbformat": 4, "nbformat_minor": 4}