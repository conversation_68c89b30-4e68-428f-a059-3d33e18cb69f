% Encoding: UTF-8



@Book{Dennis1996,
  Title                    = {Numerical Methods for Unconstrained Optimization and Nonlinear Equations},
  Author                   = {<PERSON>, J.<PERSON> and <PERSON><PERSON><PERSON><PERSON>, R.B.},
  Publisher                = {Society for Industrial and Applied Mathematics},
  Year                     = {1996},
  Series                   = {Classics in Applied Mathematics},

  ISBN                     = {9780898713640},
  Lccn                     = {lc95051776},
  Url                      = {https://books.google.de/books?id=RtxcWd0eBD0C&redir_esc=y}
}

@book{Hansen2019,
  title     = {Econometrics},
  author    = {<PERSON>},
  editor    = {<PERSON>},
  publisher = {Unpublished},
  year      = {2019},
  url       = {https://www.ssc.wisc.edu/~bhansen/econometrics/},
  owner     = {janos},
  timestamp = {2019.10.03}
}

@book{Hansen2020,
  title     = {Econometrics},
  author    = {<PERSON>},
  editor    = {<PERSON>},
  publisher = {Unpublished},
  year      = {2020},
  address   = {https://www.ssc.wisc.edu/~bhansen/econometrics/},
  owner     = {janos},
  timestamp = {2020.03.04}
}


@book{Verbeek2008,
  title     = {A Guide to Modern Econometrics},
  author    = {Verbeek, M.},
  publisher = {Wiley},
  year      = {2008},
  isbn      = {9780470517697},
  lccn      = {2007050167},
  url       = {https://books.google.com/books?id=uEFm6pAJZhoC}
}

@book{Wassermann2006,
  title     = {All of nonparametric statistics},
  author    = {Wasserman, Larry},
  year      = {2006},
  publisher = {Springer Science \& Business Media}
}

@article{Groeneveld1994,
  author  = {Eildert Groeneveld},
  title   = {A reparameterization to improve numerical optimization in multivariate REML (co)variance component estimation},
  journal = {Genetics, Selection, Evolution : GSE},
  year    = {1994},
  volume  = {26},
  pages   = {537 - 545}
}

@article{Pinheiro1996,
  author  = {José C. Pinheiro and Douglas M. Bates},
  title   = {Unconstrained Parameterizations for Variance-Covariance Matrices},
  journal = {Statistics and Computing},
  year    = {1996},
  volume  = {6},
  pages   = {289--296}
}

@techreport{Kraft1988,
  author      = {Kraft, Dieter},
  institution = {DLR German Aerospace Center – Institute for Flight Mechanics},
  title       = {A software package for sequential quadratic programming},
  year        = {1988},
  address     = {Köln, Germany},
  url         = {http://degenerateconic.com/wp-content/uploads/2018/03/DFVLR_FB_88_28.pdf}
}

@book{Nocedal2006,
  author    = {Nocedal, Jorge and Wright, Stephen},
  publisher = {Springer Science \& Business Media},
  title     = {Numerical optimization},
  year      = {2006}
}

@incollection{Conn2000,
  author    = {Conn, AR and Gould, NI and Toint, PL},
  booktitle = {Trust region methods},
  publisher = {Siam},
  title     = {Nonlinear equations and nonlinear fitting},
  year      = {2000},
  pages     = {749--774},
  volume    = {1}
}

@article{Byrd1999,
  author    = {Byrd, Richard H and Hribar, Mary E and Nocedal, Jorge},
  journal   = {SIAM Journal on Optimization},
  title     = {An interior point algorithm for large-scale nonlinear programming},
  year      = {1999},
  number    = {4},
  pages     = {877--900},
  volume    = {9},
  publisher = {SIAM}
}

@article{Lalee1998,
  author    = {Lalee, Marucha and Nocedal, Jorge and Plantenga, Todd},
  journal   = {SIAM Journal on Optimization},
  title     = {On the implementation of an algorithm for large-scale equality constrained optimization},
  year      = {1998},
  number    = {3},
  pages     = {682--706},
  volume    = {8},
  publisher = {SIAM}
}

@article{Gao2012,
  author    = {Gao, Fuchang and Han, Lixing},
  journal   = {Computational Optimization and Applications},
  title     = {Implementing the Nelder-Mead simplex algorithm with adaptive parameters},
  year      = {2012},
  number    = {1},
  pages     = {259--277},
  volume    = {51},
  publisher = {Springer}
}


@article{Powell1998,
  author    = {Powell, Michael JD},
  journal   = {Acta numerica},
  title     = {Direct search algorithms for optimization calculations},
  year      = {1998},
  pages     = {287--336},
  publisher = {Cambridge University Press}
}

@article{Powell2007,
  author    = {Powell, Michael JD},
  journal   = {Mathematics Today-Bulletin of the Institute of Mathematics and its Applications},
  title     = {A view of algorithms for optimization without derivatives},
  year      = {2007},
  number    = {5},
  pages     = {170--174},
  volume    = {43},
  publisher = {Citeseer}
}

@techreport{Benson2017,
  author      = {Benson, S and McInnes, LC and Mor{\'e}, JJ and Munson, T and Sarich, J},
  institution = {Technical Report ANL/MCS-TM-322, Argonne National Laboratory},
  title       = {TAO user manual (revision 3.7)},
  year        = {2017},
  url         = {http://web.mit.edu/tao-petsc_v3.7/tao_manual.pdf}
}

@techreport{Wild2015,
  author      = {Wild, Stefan M.},
  institution = {Argonne National Laboratory},
  title       = {Solving Derivative-Free Nonlinear Least Squares Problems with POUNDERS},
  year        = {2015},
  url         = {https://doi.org/10.1137/1.9781611974683.ch40}
}

@InProceedings{Wild2008,
  author    = {Wild, Stefan M.},
  title     = {{MNH: A} Derivative-Free Optimization Algorithm Using Minimal Norm {Hessians}},
  booktitle = {Tenth Copper Mountain Conference on Iterative Methods},
  year      = {2008},
  month     = {April},
  gurl      = {https://scholar.google.com/scholar?cluster=6407907761614456217},
  url       = {http://grandmaster.colorado.edu/~copper/2008/SCWinners/Wild.pdf},
}

@misc{Cartis2018,
  author        = {Coralia Cartis and Jan Fiala and Benjamin Marteau and Lindon Roberts},
  title         = {Improving the Flexibility and Robustness of Model-Based Derivative-Free Optimization Solvers},
  year          = {2018},
  archiveprefix = {arXiv},
  eprint        = {1804.00154},
  primaryclass  = {math.OC}
}

@misc{Cartis2018a,
  author        = {Coralia Cartis and Lindon Roberts and Oliver Sheridan-Methven},
  title         = {Escaping local minima with derivative-free methods: a numerical investigation},
  year          = {2018},
  archiveprefix = {arXiv},
  eprint        = {1812.11343},
  primaryclass  = {math.OC}
}

@article{Powell2009,
  author  = {Powell, Michael JD},
  journal = {Cambridge NA Report NA2009/06, University of Cambridge, Cambridge},
  title   = {The BOBYQA algorithm for bound constrained optimization without derivatives},
  year    = {2009},
  pages   = {26--46}
}

@misc{Cartis2018b,
  author        = {Coralia Cartis and Jan Fiala and Benjamin Marteau and Lindon Roberts},
  title         = {Improving the Flexibility and Robustness of Model-Based Derivative-Free Optimization Solvers},
  year          = {2018},
  archiveprefix = {arXiv},
  eprint        = {1804.00154},
  primaryclass  = {math.OC}
}

@unpublished{Saxton2018,
  title  = {resample: Randomization-based inference in Python.},
  author = {Daniel Saxton},
  year   = {2018},
  note   = {unpublished},
  url    = {https://github.com/dsaxton/resample}
}

@article{CameronMiller2015,
  title     = {A practitioner’s guide to cluster-robust inference},
  author    = {Cameron, A Colin and Miller, Douglas L},
  journal   = {Journal of human resources},
  volume    = {50},
  number    = {2},
  pages     = {317--372},
  year      = {2015},
  publisher = {University of Wisconsin Press}
}

@article{Waechter2005,
  author    = {Andreas Wächter and Lorenz T. Biegler},
  journal   = {{SIAM} Journal on Optimization},
  title     = {Line Search Filter Methods for Nonlinear Programming: Local Convergence},
  year      = {2005},
  month     = {jan},
  number    = {1},
  pages     = {32--48},
  volume    = {16},
  doi       = {10.1137/s1052623403426544},
  publisher = {Society for Industrial {\&} Applied Mathematics ({SIAM})}
}

@article{Waechter2005a,
  author    = {Andreas Wächter and Lorenz T. Biegler},
  journal   = {{SIAM} Journal on Optimization},
  title     = {Line Search Filter Methods for Nonlinear Programming: Motivation and Global Convergence},
  year      = {2005},
  month     = {jan},
  number    = {1},
  pages     = {1--31},
  volume    = {16},
  doi       = {10.1137/s1052623403426556},
  publisher = {Society for Industrial {\&} Applied Mathematics ({SIAM})}
}

@article{Waechter2005b,
  author    = {Andreas Wächter and Lorenz T. Biegler},
  journal   = {Mathematical Programming},
  title     = {On the implementation of an interior-point filter line-search algorithm for large-scale nonlinear programming},
  year      = {2005},
  month     = {apr},
  number    = {1},
  pages     = {25--57},
  volume    = {106},
  doi       = {10.1007/s10107-004-0559-y},
  publisher = {Springer Science and Business Media {LLC}}
}

@article{Nocedal2009,
  author    = {Jorge Nocedal and Andreas Wächter and Richard A. Waltz},
  journal   = {{SIAM} Journal on Optimization},
  title     = {Adaptive Barrier Update Strategies for Nonlinear Interior Methods},
  year      = {2009},
  month     = {jan},
  number    = {4},
  pages     = {1674--1693},
  volume    = {19},
  doi       = {10.1137/060649513},
  publisher = {Society for Industrial {\&} Applied Mathematics ({SIAM})}
}

@article{Schlueter2009,
  author    = {Martin Schlüter and Jose A. Egea and Julio R. Banga},
  journal   = {Computers {\&} Operations Research},
  title     = {Extended ant colony optimization for non-convex mixed integer nonlinear programming},
  year      = {2009},
  month     = {jul},
  number    = {7},
  pages     = {2217--2229},
  volume    = {36},
  doi       = {10.1016/j.cor.2008.08.015},
  publisher = {Elsevier {BV}}
}

@article{Karaboga2007,
  author    = {Dervis Karaboga and Bahriye Basturk},
  journal   = {Journal of Global Optimization},
  title     = {A powerful and efficient algorithm for numerical function optimization: artificial bee colony ({ABC}) algorithm},
  year      = {2007},
  month     = {apr},
  number    = {3},
  pages     = {459--471},
  volume    = {39},
  doi       = {10.1007/s10898-007-9149-x},
  publisher = {Springer Science and Business Media {LLC}}
}

@article{Mernik2015,
  author    = {Marjan Mernik and Shih-Hsi Liu and Dervis Karaboga and Matej {\v{C}}repin{\v{s}}ek},
  journal   = {Information Sciences},
  title     = {On clarifying misconceptions when comparing variants of the Artificial Bee Colony Algorithm by offering a new implementation},
  year      = {2015},
  month     = {jan},
  pages     = {115--127},
  volume    = {291},
  doi       = {10.1016/j.ins.2014.08.040},
  publisher = {Elsevier {BV}}
}

@article{Storn1997,
  author    = {Rainer Storn and Kenneth Price},
  journal   = {Journal of Global Optimization},
  title     = {Differential Evolution – A Simple and Efficient Heuristic for Global Optimization over Continuous Spaces},
  year      = {1997},
  number    = {4},
  pages     = {341--359},
  volume    = {11},
  url       = {https://link.springer.com/article/10.1023/A:1008202821328},
  publisher = {Springer Science and Business Media {LLC}}
}

@article{Oliveto2007,
  author    = {Pietro S. Oliveto and Jun He and Xin Yao},
  journal   = {International Journal of Automation and Computing},
  title     = {Time complexity of evolutionary algorithms for combinatorial optimization: A decade of results},
  year      = {2007},
  month     = {jul},
  number    = {3},
  pages     = {281--293},
  volume    = {4},
  doi       = {10.1007/s11633-007-0281-3},
  publisher = {Springer Science and Business Media {LLC}}
}


@article{Brest2006,
  author  = {Brest, Janez and Greiner, Sao and Boskovic, Borko and Mernik, Marjan and Zumer, Viljem},
  journal = {IEEE Transactions on Evolutionary Computation},
  title   = {Self-Adapting Control Parameters in Differential Evolution: A Comparative Study on Numerical Benchmark Problems},
  year    = {2006},
  number  = {6},
  pages   = {646-657},
  volume  = {10},
  doi     = {10.1109/TEVC.2006.872133}
}

@inproceedings{Elsayed2011,
  author    = {Elsayed, Saber M. and Sarker, Ruhul A. and Essam, Daryl L.},
  booktitle = {2011 IEEE Congress of Evolutionary Computation (CEC)},
  title     = {Differential evolution with multiple strategies for solving CEC2011 real-world numerical optimization problems},
  year      = {2011},
  pages     = {1041-1048},
  doi       = {10.1109/CEC.2011.5949732}
}

@incollection{Hansen2006,
  author    = {Nikolaus Hansen},
  booktitle = {Towards a New Evolutionary Computation},
  publisher = {Springer Berlin Heidelberg},
  title     = {The {CMA} Evolution Strategy: A Comparing Review},
  year      = {2006},
  pages     = {75--102},
  doi       = {10.1007/3-540-32494-1_4}
}

@article{Corana1987,
  author     = {Corana, A. and Marchesi, M. and Martini, C. and Ridella, S.},
  title      = {Minimizing Multimodal Functions of Continuous Variables with the “Simulated Annealing” Algorithm—Corrigenda for This Article is Available Here},
  year       = {1987},
  issue_date = {Sept. 1987},
  publisher  = {Association for Computing Machinery},
  address    = {New York, NY, USA},
  volume     = {13},
  number     = {3},
  issn       = {0098-3500},
  url        = {https://doi.org/10.1145/29380.29864},
  doi        = {10.1145/29380.29864},
  abstract   = {A new global optimization algorithm for functions of continuous variables is presented,
derived from the “Simulated Annealing” algorithm recently introduced in combinatorial
optimization.The algorithm is essentially an iterative random search procedure with
adaptive moves along the coordinate directions. It permits uphill moves under the
control of a probabilistic criterion, thus tending to avoid the first local minima
encountered.The algorithm has been tested against the Nelder and Mead simplex method
and against a version of Adaptive Random Search. The test functions were Rosenbrock
valleys and multiminima functions in 2,4, and 10 dimensions.The new method proved
to be more reliable than the others, being always able to find the optimum, or at
least a point very close to it. It is quite costly in term of function evaluations,
but its cost can be predicted in advance, depending only slightly on the starting
point.},
  journal    = {ACM Trans. Math. Softw.},
  month      = sep,
  pages      = {262–280},
  numpages   = {19}
}

@Article{Poli2007,
  author    = {Riccardo Poli and James Kennedy and Tim Blackwell},
  journal   = {Swarm Intelligence},
  title     = {Particle swarm optimization},
  year      = {2007},
  month     = {aug},
  number    = {1},
  pages     = {33--57},
  volume    = {1},
  doi       = {10.1007/s11721-007-0002-0},
  publisher = {Springer Science and Business Media {LLC}},
}

@Article{Wales1997,
  author    = {David J. Wales and Jonathan P. K. Doye},
  journal   = {The Journal of Physical Chemistry A},
  title     = {Global Optimization by Basin-Hopping and the Lowest Energy Structures of Lennard-Jones Clusters Containing up to 110 Atoms},
  year      = {1997},
  month     = {jul},
  number    = {28},
  pages     = {5111--5116},
  volume    = {101},
  publisher = {American Chemical Society ({ACS})},
}

@InProceedings{Glasmachers2010,
  author    = {Tobias Glasmachers and Tom Schaul and Sun Yi and Daan Wierstra and Jürgen Schmidhuber},
  booktitle = {Proceedings of the 12th annual conference on Genetic and evolutionary computation - {GECCO} {\textquotesingle}10},
  title     = {Exponential natural evolution strategies},
  year      = {2010},
  publisher = {{ACM} Press},
  doi       = {10.1145/1830483.1830557},
}

@Article{Mahdavi2007,
  author    = {M. Mahdavi and M. Fesanghary and E. Damangir},
  journal   = {Applied Mathematics and Computation},
  title     = {An improved harmony search algorithm for solving optimization problems},
  year      = {2007},
  month     = {may},
  number    = {2},
  pages     = {1567--1579},
  volume    = {188},
  doi       = {10.1016/j.amc.2006.11.033},
  publisher = {Elsevier {BV}},
}

@Article{Mirjalili2014,
  author    = {Seyedali Mirjalili and Seyed Mohammad Mirjalili and Andrew Lewis},
  journal   = {Advances in Engineering Software},
  title     = {Grey Wolf Optimizer},
  year      = {2014},
  month     = {mar},
  pages     = {46--61},
  volume    = {69},
  doi       = {10.1016/j.advengsoft.2013.12.007},
  publisher = {Elsevier {BV}},
}

@Article{Kolda2003,
  author    = {Tamara G. Kolda and Robert Michael Lewis and Virginia Torczon},
  journal   = {{SIAM} Review},
  title     = {Optimization by Direct Search: New Perspectives on Some Classical and Modern Methods},
  year      = {2003},
  month     = {jan},
  number    = {3},
  pages     = {385--482},
  volume    = {45},
  doi       = {10.1137/s003614450242889},
}

@Article{Biscani2020,
  doi = {10.21105/joss.02338},
  url = {https://doi.org/10.21105/joss.02338},
  year = {2020},
  publisher = {The Open Journal},
  volume = {5},
  number = {53},
  pages = {2338},
  author = {Francesco Biscani and Dario Izzo},
  title = {A parallel global multiobjective framework for optimization: pagmo},
  journal = {Journal of Open Source Software}
}

@Article{Coleman1996,
  author    = {Thomas F. Coleman and Yuying Li},
  journal   = {SIAM Journal on Optimization},
  title     = {An Interior Trust Region Approach for Nonlinear Minimization Subject to Bounds},
  year      = {1996},
  month     = {may},
  number    = {2},
  pages     = {418--445},
  volume    = {6},
  doi       = {10.1137/0806023},
  publisher = {Society for Industrial {\&} Applied Mathematics ({SIAM})},
}

@Article{Coleman1994,
  author    = {Thomas F. Coleman and Yuying Li},
  journal   = {Mathematcial Programming},
  title     = {On the convergence of interior-reflective Newton methods for nonlinear minimization subject to bounds},
  year      = {1994},
  month     = {oct},
  number    = {1-3},
  pages     = {189--224},
  volume    = {67},
  doi       = {10.1007/bf01582221},
  publisher = {Springer Science and Business Media {LLC}},
}

@Article{Broyden1965,
  author    = {C. G. Broyden},
  journal   = {Mathematics of Computation},
  title     = {A class of methods for solving nonlinear simultaneous equations},
  year      = {1965},
  number    = {92},
  pages     = {577--577},
  volume    = {19},
  publisher = {American Mathematical Society ({AMS})},
}

@Book{Nocedal1999,
  editor    = {Jorge Nocedal and Stephen J. Wright},
  publisher = {Springer-Verlag},
  title     = {Numerical Optimization},
  year      = {1999},
  doi       = {10.1007/b98874},
}

@Article{Fletcher1987,
  author    = {R. Fletcher and C. Xu},
  journal   = {IMA Journal of Numerical Analysis},
  title     = {Hybrid Methods for Nonlinear Least Squares},
  year      = {1987},
  number    = {3},
  pages     = {371--389},
  volume    = {7},
  doi       = {10.1093/imanum/7.3.371},
  publisher = {Oxford University Press ({OUP})},
}

@Article{Kaelo2006,
  author  = {Kaelo, P and Ali, M},
  journal = {J. Optim. Theory Appl},
  title   = {Some variants of the controlled random search algorithm for global optimization},
  year    = {2006},
  number  = {2},
  pages   = {253--264},
  volume  = {130},
}

@InBook{Price1978,
  author    = {Price, W},
  editor    = {L. C. W. Dixon and G. P. Szego},
  pages     = {71--84},
  publisher = {North-Holland Press},
  title     = {A controlled random search procedure for global optimization},
  year      = {1978},
  address   = {Amsterdam},
  volume    = {2},
  booktitle = {Towards Global Optimization},
}

@Article{Price1983,
  author  = {Price, W},
  journal = {J. Optim. Theory Appl},
  title   = {Global optimization by controlled random search},
  year    = {1983},
  number  = {3},
  pages   = {333--348},
  volume  = {40},
}

@Article{Kraft1994,
  author  = {Kraft, Dieter},
  journal = {ACM Transactions on Mathematical Software},
  title   = {Algorithm 733: TOMP-Fortran modules for optimal control calculations},
  year    = {1994},
  number  = {3},
  pages   = {262--281},
  volume  = {20},
}

@Article{Jones1993,
  author  = {Jones, D and Perttunen, C and Stuckmann, B},
  journal = {J. Optimization Theory and Applications},
  title   = {Lipschitzian optimization without the lipschitz constant},
  year    = {1993},
  pages   = {157},
  volume  = {79},
}

@Article{Gablonsky2001,
  author  = {Gablonsky, J and Kelley, C},
  journal = {J. Global Optimization},
  title   = {A locally-biased form of the DIRECT algorithm},
  year    = {2001},
  number  = {1},
  pages   = {27--37},
  volume  = {21},
}

@Article{DaSilva2010,
  author  = {Da Silva, C and Santos, M and Goncalves, H and Hernandez-Figueroa},
  journal = {IEEE Photonics Technology Letters},
  title   = {Designing Novel Photonic Devices by Bio-Inspired Computing},
  year    = {2010},
  number  = {15},
  pages   = {1177--1179},
  volume  = {22},
}

@Misc{DaSilva2010a,
  author = {Da Silva, C and Santos},
  title  = {Parallel and Bio-Inspired Computing Applied to Analyze Microwave and Photonic Metamaterial Strucutures},
  year   = {2010},
}

@Article{Beyer2002,
  author  = {Beyer, H.-G and Schwefel, H.-P},
  journal = {Journal Natural Computing},
  title   = {Evolution Strategies: A Comprehensive Introduction},
  year    = {2002},
  number  = {1},
  pages   = {3--52},
  volume  = {1},
}

@article{Vent1975,
author = {Vent, W.},
title = {Rechenberg, Ingo, Evolutionsstrategie — Optimierung technischer Systeme nach Prinzipien der biologischen Evolution. 170 S. mit 36 Abb. Frommann-Holzboog-Verlag. Stuttgart 1973. Broschiert},
journal = {Feddes Repertorium},
volume = {86},
number = {5},
pages = {337-337},
year = {1975}
}


@Article{PhilipRunarsson2005,
  author  = {Philip Runarsson, Thomas and Yao, Xin},
  journal = {IEEE Trans. on Systems, Man, and Cybernetics Part C: Applications and Reviews},
  title   = {Search biases in constrained evolutionary optimization},
  year    = {2005},
  number  = {2},
  pages   = {233--243},
  volume  = {35},
}

@Article{Thomas2000,
  author  = {Thomas, P and Runarsson, Xin and Yao},
  journal = {IEEE Trans. Evolutionary Computation},
  title   = {Stochastic ranking for constrained evolutionary optimization},
  year    = {2000},
  number  = {3},
  pages   = {284--294},
  volume  = {4},
}


@Article{Nelder1965,
  author  = {Nelder, J and Mead, R},
  journal = {The Computer Journal},
  title   = {A simplex method for function minimization},
  year    = {1965},
  pages   = {308--313},
  volume  = {7},
}

@Misc{Brent1972,
  author    = {Brent, Richard},
  title     = {Algorithms for Minimization without Derivatives},
  year      = {1972},
  publisher = {Prentice-Hall},
}

@InBook{Powell1994,
  author    = {Powell, M},
  editor    = {S. Gomez and J.-P. Hennart},
  pages     = {51--67},
  publisher = {Kluwer Academic},
  title     = {A direct search optimization method that models the objective and constraint functions by linear interpolation},
  year      = {1994},
  address   = {Dordrecht},
  booktitle = {Advances in Optimization and Numerical Analysis},
}

@Misc{Rowan1990,
  author = {Rowan, T},
  title  = {Functional Stability Analysis of Numerical Algorithms},
  year   = {1990},
}

@InProceedings{Powell2004,
  author    = {Powell, M},
  booktitle = {Proc. 40th Workshop on Large Scale Nonlinear Optimization},
  title     = {The NEWUOA software for unconstrained optimization without derivatives},
  year      = {2004},
  address   = {Erice, Italy},
}

@Article{Dembo1983,
  author  = {Dembo, R and Steihaug, T},
  journal = {Math. Programming},
  title   = {Truncated Newton algorithms for large-scale optimization},
  year    = {1983},
  pages   = {190--212},
  volume  = {26},
  doi     = {10.1007/BF02592055},
}

@Article{Nocedal1989,
  author  = {Nocedal ; D, J and Liu, J},
  journal = {Math. Comput},
  title   = {On the limited memory BFGS method for large scale optimization},
  year    = {1989},
  pages   = {503--528},
  volume  = {35},
}

@Article{Svanberg2002,
  author  = {Svanberg, Krister},
  journal = {SIAM J. Optim},
  title   = {A class of globally convergent optimization methods based on conservative convex separable approximations},
  year    = {2002},
  number  = {2},
  pages   = {555--573},
  volume  = {12},
}

@Article{Vlcek2006,
  author  = {Vlcek, J and Luksan, L},
  journal = {J. Computational Appl. Math},
  title   = {Shifted limited-memory variable metric methods for large-scale unconstrained minimization},
  year    = {2006},
  pages   = {365--390},
  volume  = {186},
}

@Article{Nocedal1980,
  author  = {Nocedal, J.},
  journal = {Math. Comput},
  title   = {Updating quasi-Newton matrices with limited storage},
  year    = {1980},
  pages   = {773--782},
  volume  = {35},

}

@InProceedings{Chiang2014,
  author    = {Chiang, N and Petra, C and Zavala, V},
  booktitle = {Proceedings of the 18th power systems computation conference (PSCC)},
  title     = {Structured nonconvex optimization of large-scale energy systems using PIPS-NLP},
  year      = {2014},
  address   = {Wroclaw, Poland},
}


@Article{Zhou2010,
  author    = {Weijun Zhou and Xiaojun Chen},
  journal   = {SIAM Journal on Optimization},
  title     = {Global Convergence of a New Hybrid Gauss{\textendash}Newton Structured {BFGS} Method for Nonlinear Least Squares Problems},
  year      = {2010},
  month     = {jan},
  number    = {5},
  pages     = {2422--2441},
  volume    = {20},
  doi       = {10.1137/090748470},
  publisher = {Society for Industrial {\&} Applied Mathematics ({SIAM})},
}

@Article{Dennis1989,
  author    = {J. E. Dennis and H. J. Martinez and R. A. Tapia},
  journal   = {Journal of Optimization Theory and Applications},
  title     = {Convergence theory for the structured {BFGS} secant method with an application to nonlinear least squares},
  year      = {1989},
  month     = {may},
  number    = {2},
  pages     = {161--178},
  volume    = {61},
  doi       = {10.1007/bf00962795},
  publisher = {Springer Science and Business Media {LLC}},
}

@Article{Huschens1994,
  author    = {J. Huschens},
  journal   = {SIAM Journal on Optimization},
  title     = {On the Use of Product Structure in Secant Methods for Nonlinear Least Squares Problems},
  year      = {1994},
  month     = {feb},
  number    = {1},
  pages     = {108--129},
  volume    = {4},
  doi       = {10.1137/0804005},
  publisher = {Society for Industrial {\&} Applied Mathematics ({SIAM})},
}

@Article{Berndt1974,
  title = {Estimation and Inference in Nonlinear Structural Models},
  journal = {Annals of Economic and Social Measurement},
  author = {Berndt, Ernst R. and Hall, Bronwyn and Hall, Robert and Hausman, Jerry},
  year = {1974},
  pages = {653-665},
  booktitle = {Annals of Economic and Social Measurement, Volume 3, number 4},
  publisher = {National Bureau of Economic Research, Inc},
}

@Article{Halbert1982,
 author = {Halbert White},
 journal = {Econometrica},
 number = {1},
 pages = {1--25},
 publisher = {[Wiley, Econometric Society]},
 title = {Maximum Likelihood Estimation of Misspecified Models},
 volume = {50},
 year = {1982},
}


@article{More1983,
  title={Computing a Trust Region Step},
  author={Jorge J. Mor{\'e} and Danny C. Sorensen},
  journal={Siam Journal on Scientific and Statistical Computing},
  year={1983},
  volume={4},
  pages={553-572}
}

@article{Bertsekas1982,
author = {Bertsekas, Dimitri P.},
title = {Projected Newton Methods for Optimization Problems with Simple Constraints},
journal = {SIAM Journal on Control and Optimization},
volume = {20},
number = {2},
pages = {221-246},
year = {1982},
doi = {10.1137/0320018},
URL = {https://doi.org/10.1137/0320018},
}

@article{Steihaug1983,
author = {Steihaug, Trond},
title = {The Conjugate Gradient Method and Trust Regions in Large Scale Optimization},
journal = {SIAM Journal on Numerical Analysis},
volume = {20},
number = {3},
pages = {626-637},
year = {1983},
doi = {10.1137/0720042},
URL = {https://doi.org/10.1137/0720042},
}

@InBook{Toint1981,
  title      = {Towards an Efficient Sparsity Exploiting Newton Method for Minimization},
  author     = {Toint, {\relax Ph}ilippe L.},
  booktitle  = {Sparse Matrices and Their Uses},
  publisher  = {Academic Press},
  year       = {1981},
  address    = {London, England},
  editor     = {I. S. Duff},
  pages      = {57--88},
}

@article{Zhang2010,
author = {Zhang, Hongchao and Conn, Andrew R. and Scheinberg, Katya},
title = {A Derivative-Free Algorithm for Least-Squares Minimization},
journal = {SIAM Journal on Optimization},
volume = {20},
number = {6},
pages = {3555-3576},
year = {2010},
doi = {10.1137/09075531X},
URL = {https://doi.org/10.1137/09075531X},
}


@book{Conn2009,
author = {Conn, Andrew R. and Scheinberg, Katya and Vicente, Luis N.},
title = {Introduction to Derivative-Free Optimization},
publisher = {Society for Industrial and Applied Mathematics},
year = {2009},
doi = {10.1137/1.9780898718768},
URL = {https://epubs.siam.org/doi/abs/10.1137/1.9780898718768},
}

@article{JAMES1975343,
title = {Minuit - a system for function minimization and analysis of the parameter errors and correlations},
journal = {Computer Physics Communications},
volume = {10},
number = {6},
pages = {343-367},
year = {1975},
issn = {0010-4655},
doi = {https://doi.org/10.1016/0010-4655(75)90039-9},
url = {https://www.sciencedirect.com/science/article/pii/0010465575900399},
author = {F. James and M. Roos}
}

@InProceedings{Kennedy1995,
  author={Kennedy, J. and Eberhart, R.},
  booktitle={Proceedings of ICNN'95 - International Conference on Neural Networks}, 
  title={Particle swarm optimization}, 
  year={1995},
  volume={4},
  pages={1942-1948 vol.4},
  keywords={Particle swarm optimization;Birds;Educational institutions;Marine animals;Testing;Humans;Genetic algorithms;Optimization methods;Artificial neural networks;Performance evaluation},
  doi={10.1109/ICNN.1995.488968},
}

@InProceedings{Zambrano2013,
  author = {Zambrano-Bigiarini, Mauricio and Clerc, Maurice and Rojas, Rodrigo},
  booktitle = {2013 IEEE Congress on Evolutionary Computation}, 
  title = {Standard Particle Swarm Optimisation 2011 at CEC-2013: A baseline for future PSO improvements}, 
  year = {2013},
  pages = {2337-2344},
  keywords = {Optimization;Standards;Benchmark testing;Topology;Algorithm design and analysis;Convergence;Equations;particle swarm optimization;SPSO-2011;CEC-2013;random topology;rotational invariance;benchmark testing;evolutionary computation;optimization},
  doi = {10.1109/CEC.2013.6557848},
}

@Misc{Nogueira2014,
  author={Fernando Nogueira},
  title={{Bayesian Optimization}: Open source constrained global optimization tool for {Python}},
  year={2014--},
  url="https://github.com/bayesian-optimization/BayesianOptimization"
}

@article{Stander2002,
  author={Stander, Nielen and Craig, Kenneth},
  year={2002},
  month={06},
  pages={},
  title={On the robustness of a simple domain reduction scheme for simulation-based optimization},
  volume={19},
  journal={International Journal for Computer-Aided Engineering and Software (Eng. Comput.)},
  doi={10.1108/02644400210430190}
}

@inproceedings{gardner2014bayesian,
  title={Bayesian optimization with inequality constraints.},
  author={Gardner, Jacob R and Kusner, Matt J and Xu, Zhixiang Eddie and Weinberger, Kilian Q and Cunningham, John P},
  booktitle={ICML},
  volume={2014},
  pages={937--945},
  year={2014}
}

@Comment{jabref-meta: databaseType:bibtex;}
