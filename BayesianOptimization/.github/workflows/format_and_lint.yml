name: Code format and lint

on:
  push:
    branches: [ "master" ]
  pull_request:

permissions:
  contents: read

jobs:
  check:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python 3.9
      uses: actions/setup-python@v3
      with:
        python-version: "3.9"
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: 'latest'
    - name: Install dependencies
      run: |
        poetry install --with dev
    - name: Run pre-commit
      run : poetry run pre-commit run --all-files --show-diff-on-failure --color=always
