{"cells": [{"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["# How to add optimizers to optimagic\n", "\n", "This is a hands-on guide that shows you how to use custom optimizers with optimagic or\n", "how to contribute an optimizer to the optimagic library.\n", "\n", "We have many [examples of optimizers](https://github.com/optimagic-dev/optimagic/tree/main/src/optimagic/optimizers) that are already part of optimagic and you can learn a lot from looking at \n", "those. However, only looking at the final results might be a bit intimidating and does\n", "not show the process of exploring a new optimizer library and gradually developing a \n", "wrapper. \n", "\n", "This guide is there to fill the gap. It tells the story of how the `pygmo_gaco`\n", "optimizer was added to optimagic by someone who was unfamiliar with pygmo or the \n", "gaco algorithm. \n", "\n", "The steps of adding an algorithm are roughly as follows:\n", "\n", "1. **Understand how to use the algorithm**: Play around with the algorithm you want to \n", "add in a notebook and solve some simple problems with it. Only move on to the next step \n", "after you have a solid understanding. This is completely unrelated to optimagic and only\n", "about he algorithm implementation you want to wrap. \n", "2. **Understand how the algorithm works**: Read documentation,\n", "research papers and other resources to find out why this algorithm was created and what \n", "problems it is supposed to solve really well. \n", "3. **Implement the minimal wrapper**: <PERSON>rn about the `om.mark.minimizer` decorator as \n", "well as the `om.InternalOptimizationProblem` and the `om.Algorithm` classes. Implement a \n", "minimal version of your wrapper and test it.\n", "4. **Complete and refactor the wrapper**: Make sure that all convergence criteria, \n", "stopping criteria, and tuning parameters the algorithm supports can be passed to your \n", "wrapper. Also check that the algorithm gets everything it needs to achieve maximum \n", "performance (e.g. closed form derivatives and batch function evaluators). Now is also \n", "the time to clean-up and refactor your code, especially if you wrap multiple optimizers \n", "from a library.\n", "5. **Align the wrapper with optimagic conventions**: Use harmonized names wherever \n", "a convention exists. Think about good names everywhere else. Set stopping criteria \n", "similar to other optimizers and try to adhere to our [design philosophy](style_guide) \n", "when it comes to tuning parameters. \n", "6. **Integrate your code into optimagic**: Learn how to add an optional dependency to \n", "optimagic, where you need to put your code and how to add tests and documentation. \n", "\n", "\n", "## Gen AI Policy \n", "\n", "It is ok to use GenAI and AI based coding assistants to speed up the process of adding \n", "an optimizer to optimagic. They can be very useful for step 1 and 2. However, AI models \n", "often fail completely when filling out the arguments of `om.mark.minimizer`, when you \n", "ask them to come up with good names for tuning parameters or when you auto-generate the \n", "documentation.  \n", "\n", "Even for step 1 and 2 you should not use an AI Model naively, but upload a paper or \n", "documentation page to provide context to the AI.\n", "\n", "Our policy is therefore:\n", "1. Only use AI for drafts that you double-check; Never rely on AI producing correct results \n", "2. Be transparent about your use of AI \n", "\n", "We will reject all Pull Requests that violate this policy. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Understand how to use the algorithm\n", "\n", "Understanding how to use an algorithm means that you are at least able to solve a \n", "simple optimization problem (like a sphere function or a rosenbrock function). \n", "\n", "The best starting point for this are usually tutorials or example notebooks from the \n", "documentation. An AI model can also be a good idea. \n", "\n", "The things you need to find out for any new algorithm are:\n", "\n", "1. How to code up the objective function \n", "2. How to run an optimization at default values\n", "3. How to pass tuning parameters \n", "4. How to pass bounds, constraints, derivatives, batch evaluators, etc. \n", "5. How to get results back from the optimizer\n", "\n", "### Objective functions in pygmo\n", "\n", "To add pygmo_gaco, let's start by looking at the pygmo [tutorials](https://esa.github.io/pygmo2/tutorials/tutorials.html). Objective functions are coded up via the Problem class. We skip using [pre-defined problems](https://esa.github.io/pygmo2/tutorials/using_problem.html) because they will not help us and directly go to [user defined problems](https://esa.github.io/pygmo2/tutorials/coding_udp_simple.html).\n", "\n", "The following is copied from the documentation:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pygmo as pg\n", "\n", "\n", "class sphere_function:\n", "    def fitness(self, x):\n", "        return [sum(x * x)]\n", "\n", "    def get_bounds(self):\n", "        return ([-1, -1], [1, 1])\n", "\n", "\n", "prob = pg.problem(sphere_function())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This looks simple enough. No subclassing is required, `fitness` implements the objective\n", "function, which returns the objective value as a list of a scalar and `get_bounds` returns \n", "the bounds. We can immediately see how we would adjust this for any scalar objective \n", "function. \n", "\n", "### How to run an optimization at default values\n", "\n", "After copy pasting from a few tutorials we find the following:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# The initial population\n", "pop = pg.population(prob, size=20)\n", "# The algorithm; ker needs to be at most the population size to avoid errors\n", "algo = pg.algorithm(pg.gaco(ker=20))\n", "# The actual optimization process\n", "pop = algo.evolve(pop)\n", "# Getting the best individual in the population\n", "best_fitness = pop.get_f()[pop.best_idx()]\n", "print(best_fitness)\n", "best_x = pop.get_x()[pop.best_idx()]\n", "print(np.round(best_x, 4))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It looks like the optimization worked, even though the precision is not great. The true optimal function value is 0 and the true optimal parameters are [0, 0]. But global algorithms like gaco are almost never precise, so this is good enough. \n", "\n", "We can also see that pygmo is really organized around concepts that are specific to genetic optimizers. Examples are `population` and `evolve`. The optimagic wrapper will hide the details (i.e. users don't have to create a population) but still allow full customization (the population size will be an algorithm specific option that can be set by the user).\n", "\n", "### How to pass tuning parameters\n", "\n", "We already saw in the previous step that tuning parameters like `ker` are passed when the \n", "algorithm is created. \n", "\n", "All supported tuning parameters of gaco are listed and described \n", "[here](https://esa.github.io/pygmo2/algorithms.html#pygmo.gaco). Unfortunately, the \n", "description is not great so we'll have to look into the [paper](https://digital.csic.es/bitstream/10261/54957/3/Extended_ant_colony_2009.pdf) for details. \n", "\n", "\n", "### How to pass bounds, constraints, derivatives, batch evaluators, etc. \n", "\n", "- We already saw how to pass bounds via the Problem class \n", "- gaco does not support any other constraints, so we don't need to pass them \n", "- gaco is derivative free, so we don't need to pass derivatives \n", "- gaco can parallelize, so we need to find out how to pass a batch version of the \n", "objective function\n", "\n", "After searching around in the pygmo documentation, we find out that our Problem needs to \n", "be extended with a [`batch_fitness`](https://esa.github.io/pygmo2/problem.html#pygmo.problem.batch_fitness)\n", "and our algorithm needs to know about [`pg.bfe()`](https://esa.github.io/pygmo2/bfe.html).\n", "In our previous example it will look like this:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pygmo as pg\n", "\n", "\n", "class sphere_function:\n", "    def fitness(self, x):\n", "        return [sum(x * x)]\n", "\n", "    def get_bounds(self):\n", "        return ([-1, -1], [1, 1])\n", "\n", "    # dvs represents a batch of parameter vectors at which the objective function is\n", "    # evaluated. However it is stored in an unintuitive format that needs to be reshaped\n", "    # to get at the actual parameter vectors.\n", "    def batch_fitness(self, dvs):\n", "        dim = len(self.get_bounds()[0])\n", "        x_list = list(dvs.reshape(-1, dim))\n", "        # we don't actually need to parallelize to find out how batch evaluators work\n", "        # and optimagic will make it really easy to parallelize this later on.\n", "        eval_list = [self.fitness(x)[0] for x in x_list]\n", "        evals = np.array(eval_list)\n", "        return evals\n", "\n", "\n", "prob = pg.problem(sphere_function())\n", "\n", "pop = pg.population(prob, size=20)\n", "\n", "# creating the algorithm now requires 3 steps\n", "pygmo_uda = pg.gaco(ker=20)\n", "pygmo_uda.set_bfe(pg.bfe())\n", "algo = pg.algorithm(pygmo_uda)\n", "\n", "pop = algo.evolve(pop)\n", "best_fitness = pop.get_f()[pop.best_idx()]\n", "print(best_fitness)\n", "best_x = pop.get_x()[pop.best_idx()]\n", "print(np.round(best_x, 4))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["For this how-to guide we leave it at this basic exploration of the pygmo library. If you actually contributed an optimizer to optimagic, you would have to explore much more and document your exploration to convince us that you understand the library you wrap in detail. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### How to get results back \n", "\n", "The results are stored as part of the evolved population"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Best function value: \", pop.get_f()[pop.best_idx()][0])\n", "print(\"Best parameters: \", pop.get_x()[pop.best_idx()])\n", "print(\"Number of function evaluations: \", pop.problem.get_fevals())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Understand how the algorithm works\n", "\n", "Here we want to find out as much as possible about the algorithm. Common questions \n", "that should be answered are:\n", "- For which kind of problems and situations was it designed?\n", "- How does it work (intuitively)?\n", "- Are there any papers, blogposts or other sources of information on the algorithm? \n", "- Which tuning parameters does it have and what do they mean? \n", "- Are there known limitations? \n", "\n", "### For which kind of problems and situations was it desigend \n", "\n", "gaco is a global optimizer that does not use derivative information. It should not be\n", "used if you only need a local optimum or if you have derivatives. Other algorithms would \n", "be more efficient and more precise there. \n", "\n", "Since gaco can evaluate the objective function in parallel it is designed for problems \n", "with expensive objective functions. \n", "\n", "\n", "### How does it work (intuitively)\n", "\n", "Ant colony optimization is a class of optimization algorithms modeled on the\n", "actions of an ant colony. Artificial \"ants\" (e.g. simulation agents) locate\n", "optimal solutions by moving through a parameter space representing all\n", "possible solutions. Real ants lay down pheromones directing each other to\n", "resources while exploring their environment. The simulated \"ants\" similarly\n", "record their positions and the quality of their solutions, so that in later\n", "simulation iterations more ants locate better solutions.\n", "\n", "The generalized ant colony algorithm generates future generations of ants by\n", "using a multi-kernel gaussian distribution based on three parameters (i.e.,\n", "pheromone values) which are computed depending on the quality of each\n", "previous solution. The solutions are ranked through an oracle penalty\n", "method.\n", "\n", "\n", "### Are there any papers, blogposts or other sources of information on the algorithm? \n", "\n", "gaco was proposed in <PERSON><PERSON>, et al. (2009). Extended ant colony optimization for \n", "non-convex mixed integer non-linear programming. Computers & Operations Research.\n", "\n", "See [here](https://digital.csic.es/bitstream/10261/54957/3/Extended_ant_colony_2009.pdf) for a free pdf. \n", "\n", "### Which tuning parameters does it have and what do they mean? \n", "\n", "The following is not just copied from the documentation but extended by reading the\n", "paper. It is super important to provide as much information as possible for every \n", "tunig parameter: \n", "\n", "- gen (int): number of generations.\n", "- ker (int): number of solutions stored in the solution archive. Must be <= the population\n", "    size. \n", "- q (float): convergence speed parameter. This parameter manages the convergence speed\n", "    towards the found minima (the smaller the faster). It must be positive and can be\n", "    larger than 1. The default is 1.0 until **threshold** is reached. Then it\n", "    is set to 0.01.\n", "- oracle (float): oracle parameter used in the penalty method.\n", "- acc (float): accuracy parameter for maintaining a minimum penalty\n", "    function's values distances.\n", "- threshold (int): when the iteration counter reaches the threshold the\n", "    convergence speed is set to 0.01 automatically. To deactivate this effect\n", "    set the threshold to stopping.maxiter which is the largest allowed\n", "    value.\n", "- n_gen_mark (int): parameter that determines the convergence speed of the standard \n", "    deviations. This must be an integer.\n", "- impstop (int): if a positive integer is assigned here, the algorithm will count the \n", "    runs without improvements, if this number exceeds the given value, the algorithm \n", "    will be stopped.\n", "- evalstop (int): maximum number of function evaluations.\n", "- focus (float): this parameter makes the search for the optimum greedier\n", "    and more focused on local improvements (the higher the greedier). If the\n", "    value is very high, the search is more focused around the current best\n", "    solutions. Values larger than 1 are allowed.\n", "- memory (bool): if True, memory is activated in the algorithm for multiple calls.\n", "- seed (int): seed used by the internal random number generator (default is random).\n", "\n", "\n", "### Are there known limitations \n", "\n", "No. \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Implement the minimal wrapper\n", "\n", "\n", "### Learn the relevant functions and classes\n", "\n", "Before you implement a minimal wrapper, you need to familiarize yourself with a few\n", "important [classes and functions](functions_and_classes_for_internal_optimizers) \n", "you will need. \n", "\n", "- The `mark.miminizer` decorator \n", "- The `Algorithm` class \n", "- The `InternalOptimizationProblem` class \n", "- The `InternalOptimizeResult` class \n", "\n", "**Your task will be to subclass `Algorithm`. Your subclass must be decorated with\n", "`mark.minizer` and override `Algorithm._solve_internal_problem`. `_solve_internal_problem`\n", "takes an `InternalOptimizationProblem` and returns an `InternalOptimizeResult`**\n", "\n", "```{note}\n", "Users of optimagic never create instances of `InternalOptimizationProblem` nor \n", "do they call the `_solve_internal_problem` methods of algorithms. Instead they call \n", "`minimize` or `maximize` which are much more convenient and flexible. \n", "\n", "`minimize` and `maximize` will then create an `InternalOptimizationProblem` from the \n", "user's inputs, call the `_solve_internal_problem` method and postprocess it to create an \n", "OptimizeResult. \n", "\n", "To summarize: The public `minimize` interface is optimized for user-friendliness. The \n", "`InternalOptimizeProblem` is optimized for easy wrapping of external libraries. \n", "```\n", "\n", "Below we define a heavily commented minimal version of a wrapper for pygmo's gaco \n", "algorithm. We stay as close as possible to the pygmo examples we have worked with \n", "before and ignore most tuning parameters for now. \n", "\n", "\n", "### Write the minimal implementation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass\n", "\n", "from numpy.typing import NDArray\n", "\n", "import optimagic as om\n", "from optimagic.optimization.algorithm import Algorithm, InternalOptimizeResult\n", "from optimagic.optimization.internal_optimization_problem import (\n", "    InternalOptimizationProblem,\n", ")\n", "from optimagic.typing import AggregationLevel, PositiveInt\n", "\n", "try:\n", "    import pygmo as pg\n", "\n", "    IS_PYGMO_INSTALLED = True\n", "except ImportError:\n", "    IS_PYGMO_INSTALLED = False\n", "\n", "\n", "@om.mark.minimizer(\n", "    # you can pick the name; convention is lowercase with underscores\n", "    name=\"pygmo_gaco\",\n", "    # the type of problem this optimizer can solve -> scalar problems; Other optimizers\n", "    # solve likelihood or least_squares problems.\n", "    solver_type=AggregationLevel.SCALAR,\n", "    # is the optimizer available? -> only if pygmo is installed\n", "    is_available=IS_PYGMO_INSTALLED,\n", "    # is the optimizer a global optimizer? -> yes\n", "    is_global=True,\n", "    # does the optimizer need the jacobian? -> no, gaco is derivative free\n", "    needs_jac=False,\n", "    # does the optimizer need the hessian? -> no, gaco is derivative free\n", "    needs_hess=False,\n", "    # does the optimizer support parallelism? -> yes\n", "    supports_parallelism=True,\n", "    # does the optimizer support bounds? -> yes\n", "    supports_bounds=True,\n", "    # does the optimizer support linear constraints? -> no\n", "    supports_linear_constraints=False,\n", "    # does the optimizer support nonlinear constraints? -> no\n", "    supports_nonlinear_constraints=False,\n", "    # should the history be disabled? -> no\n", "    disable_history=False,\n", ")\n", "# All algortihms need to be frozen dataclasses.\n", "@dataclass(frozen=True)\n", "class PygmoGaco(Algorithm):\n", "    # for now only set one parameter to get things running. The rest will come later.\n", "    stopping_maxiter: PositiveInt = 1000\n", "    n_cores: int = 1\n", "\n", "    def _solve_internal_problem(\n", "        self, problem: InternalOptimizationProblem, x0: NDArray[np.float64]\n", "    ) -> InternalOptimizeResult:\n", "        # create a pygmo problem from the internal optimization problem\n", "        # This is just slightly more abstract than before and actually simpler because\n", "        # we have problem.batch_fun.\n", "\n", "        n_cores = self.n_cores\n", "\n", "        class PygmoProblem:\n", "            def fitness(self, x):\n", "                # problem.fun is not just the `fun` that was passed to `minimize` by\n", "                # the user. It is a wrapper around fun with added error handling,\n", "                # history collection, and reparametrization to enforce constraints.\n", "                # Moreover, it always works on flat numpy arrays as parameters and\n", "                # does not have additional arguments. The magic of optimagic is to\n", "                # create this internal `fun` from the user's `fun`, so you don't have\n", "                # to deal with constraints, weird parameter formats and similar when\n", "                # implementing the wrapper.\n", "                return [problem.fun(x)]\n", "\n", "            def get_bounds(self):\n", "                # problem.bounds is not just the `bounds` that was passed to `minimize`\n", "                # by the user, which could have been a dictionary or some other non-flat\n", "                # format. `problem.bounds` always contains flat arrays with lower and\n", "                # upper bounds because this makes it easy to write wrappers.\n", "                return (problem.bounds.lower, problem.bounds.upper)\n", "\n", "            def batch_fitness(self, dvs):\n", "                # The processing of dvs is pygmo specific.\n", "                dim = len(self.get_bounds()[0])\n", "                x_list = list(dvs.reshape(-1, dim))\n", "                # problem.batch_fun is a parallelized version of problem.fun.\n", "                eval_list = problem.batch_fun(x_list, n_cores)\n", "                evals = np.array(eval_list)\n", "                return evals\n", "\n", "        prob = pg.problem(PygmoProblem())\n", "        pop = pg.population(prob, size=20)\n", "        pygmo_uda = pg.gaco(ker=20)\n", "        pygmo_uda.set_bfe(pg.bfe())\n", "        algo = pg.algorithm(pygmo_uda)\n", "        pop = algo.evolve(pop)\n", "        best_fun = pop.get_f()[pop.best_idx()][0]\n", "        best_x = pop.get_x()[pop.best_idx()]\n", "        n_fun_evals = pop.problem.get_fevals()\n", "        # For now we only use a few fields of the InternalOptimizeResult.\n", "        out = InternalOptimizeResult(\n", "            x=best_x,\n", "            fun=best_fun,\n", "            n_fun_evals=n_fun_evals,\n", "        )\n", "        return out"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Test the minimal wrapper directly\n", "\n", "So now that we have a wrapper, what do we do with it? And how can we be sure it works?\n", "\n", "We'll first try it out directly with the `SphereExampleInternalOptimizationProblem`. \n", "This is only for debugging and testing purposes. A user would never create an \n", "InternalOptimizationProblem and call an algorithm with it. It's called \"Internal\" for \n", "a reason!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from optimagic.optimization.internal_optimization_problem import (\n", "    SphereExampleInternalOptimizationProblem,\n", ")\n", "\n", "problem = SphereExampleInternalOptimizationProblem()\n", "\n", "gaco = PygmoGaco()\n", "\n", "result = gaco._solve_internal_problem(problem, x0=np.array([1.0, 1.0]))\n", "\n", "print(result.fun)\n", "print(result.x)\n", "print(result.n_fun_evals)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Use the minimal wrapper in minimize\n", "\n", "The internal testing gives us some confidence that the wrapper works correctly and would \n", "have been good for debugging if it didn't. But now we want to test the wrapper in the\n", "way it would be used later: via `minimize`\n", "\n", "With this we also get all the benefits of optimagic, from history collection and \n", "criterion plots to flexible parameter formats. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res = om.minimize(\n", "    fun=lambda x: x @ x,\n", "    params=np.arange(5),\n", "    algorithm=PygmoGaco,\n", "    bounds=om.Bounds(lower=-np.ones(5), upper=np.ones(5)),\n", ")\n", "\n", "om.criterion_plot(res, monotone=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4 Complete and refactor the wrapper\n", "\n", "To keep things simple, we left out almost all tuning parameters of the gaco algorithm \n", "when we wrote the minimal wrapper. \n", "\n", "Now it's time to add them. You can add them one by one and make sure nothing breaks by \n", "testing your wrapper after each change - both with the internal problem and via \n", "minimize. \n", "\n", "Moreover, our code looks quite messy currently. Despite being a minimal wrapper, the \n", "`_solve_internal_problem` method is quite long, unstructured and hard to read. \n", "\n", "The result of completing and refactoring the wrapper is too long to be repeated in the \n", "notebook. Instead you can look at the actual [implementation in optimagic](\n", "https://github.com/optimagic-dev/optimagic/blob/ba2678753587f91cea54de69ff76cb3dcb4257d4/src/optimagic/optimizers/pygmo_optimizers.py#L70)\n", "\n", "\n", "The PygmoGaco class now contains all tuning parameters we identified in step 2 as\n", "dataclass fields. They all have very useful type-hints that don't just show whether\n", "a parameter is an int, str or float but also which values it can take (e.g. PositiveInt).\n", "\n", "`_solve_internal_problem` is now also much cleaner. It mainly maps our mor descriptive \n", "names of tuning parameters to the old pygmo names and then calls a function called \n", "`_minimize_pygmo` that does all the heavy lifting and can be re-used for other pygmo \n", "optimizers. \n", "\n", "The arguments to `mark.minimizer` have not changed. They always need te be set correctly,\n", "even for minimal working examples. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Align the wrapper with optimagic conventions\n", "\n", "To make switching between different algorithm as simple as possible, we align the names \n", "of commonly used convergence and stopping criteria. We also align the default values for \n", "stopping and convergence criteria as much as possible. \n", "\n", "You can find the harmonized names and value [here](algo_options_docs). \n", "\n", "To align the names of other tuning parameters as much as possible with what is already \n", "there, simple have a look at the optimizers we already wrapped. For example, if you are \n", "wrapping a bfgs or lbfgs algorithm from some libray, try to look at all existing wrappers \n", "of bfgs algorithms and use the same names for the same options. \n", "\n", "You can see what this means for the gaco algorithm [here](\n", "https://github.com/optimagic-dev/optimagic/blob/ba2678753587f91cea54de69ff76cb3dcb4257d4/src/optimagic/optimizers/pygmo_optimizers.py#L70)\n", "\n", "In the future we will provide much more extensive guidelines for harmonization. \n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Integrate your code into optimagic\n", "\n", "So far you could have worked in a Jupyter Notebook. Integrating your code into \n", "optimagic only requires a few small changes:\n", "\n", "1. Add new dependencies to the `environment.yml` file and run \n", "`pre-commit run --all-files`. This will trigger a script that adds the dependencies to \n", "multiple environments we need for continuous integration. Then re-create the enviroment \n", "to make sure that the environment is the same as we will use for continuous integration.\n", "If your dependencies don't work on all platforms (e.g. linux only packages), skip this\n", "entire step and reach out to a core contributor for help. \n", "2. Save the code for your algorithm wrapper in a .py file in `optimagic.algorithms`. \n", "Use an existing file if you wrap another algorithm from a library we already had. \n", "Otherwise, create a new file. \n", "3. Run `pre-commit run --all-files`. This will trigger an automatic code generation \n", "that fully integrates your wrapper into our algorithm selection tool.\n", "4. Run `pytest`. This will run at least a few tests for your new algorithm. Add more \n", "tests for algorithm specific things (e.g. tests that make sure tuning parameters have \n", "the intended effects). \n", "5. Write documentation. The documentation should contain everything you figured out in  \n", "step 2. You can either write it into the docstring of your algorithm class (preferred, \n", "as this is what we will do for all algorithms in the long run) or in `algorithms.md` \n", "in the documentation.  \n", "6. Create a pull request [in the optimagic repository](https://github.com/optimagic-dev/optimagic)\n", "and ask for a review. "]}], "metadata": {"kernelspec": {"display_name": "optimagic", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}