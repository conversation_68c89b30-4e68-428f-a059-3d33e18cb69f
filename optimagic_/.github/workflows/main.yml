---
name: main
# Automatically cancel a previous run.
concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - '*'
jobs:
  run-tests-linux:
    name: Run tests for ${{ matrix.os }} on ${{ matrix.python-version }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-latest
        python-version:
          - '3.10'
          - '3.11'
          - '3.12'
          - '3.13'
    steps:
      - uses: actions/checkout@v4
      - name: create build environment
        uses: mamba-org/setup-micromamba@v1
        with:
          environment-file: ./.tools/envs/testenv-linux.yml
          cache-environment: true
          create-args: |
            python=${{ matrix.python-version }}
      - name: run pytest
        shell: bash -l {0}
        run: |
          micromamba activate optimagic
          pytest --cov-report=xml --cov=./
      - name: Upload coverage report.
        if: runner.os == 'Linux' && matrix.python-version == '3.10'
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
  run-tests-win-and-mac:
    name: Run tests for ${{ matrix.os }} on ${{ matrix.python-version }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os:
          - macos-latest
          - windows-latest
        python-version:
          - '3.10'
          - '3.11'
          - '3.12'
          - '3.13'
    steps:
      - uses: actions/checkout@v4
      - name: create build environment
        uses: mamba-org/setup-micromamba@v1
        with:
          micromamba-version: 1.5.6-0
          environment-file: ./.tools/envs/testenv-others.yml
          cache-environment: true
          create-args: |
            python=${{ matrix.python-version }}
      - name: run pytest
        shell: bash -l {0}
        run: |
          micromamba activate optimagic
          pytest -m "not slow and not jax"
  run-tests-with-old-pandas:
    # This job is only for testing if optimagic works with pandas<2, as many pandas
    # functions we use will be deprecated in pandas 3. optimagic's behavior for older
    # verions is handled in src/optimagic/compat.py. For compatibility with we have to
    # restrict numpy<2.
    name: Run tests for ${{ matrix.os}} on ${{ matrix.python-version }} with pandas 1
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-latest
        python-version:
          - '3.10'
    steps:
      - uses: actions/checkout@v4
      - name: create build environment
        uses: mamba-org/setup-micromamba@v1
        with:
          environment-file: ./.tools/envs/testenv-pandas.yml
          cache-environment: true
          create-args: |
            python=${{ matrix.python-version }}
      - name: run pytest
        shell: bash -l {0}
        run: |
          micromamba activate optimagic
          pytest -m "not slow and not jax"
  run-tests-with-old-numpy:
    # This job is only for testing if optimagic works with numpy<2. Because we already
    # test pandas<2 with numpy<2, in this environment we restrict pandas>=2.
    name: Run tests for ${{ matrix.os}} on ${{ matrix.python-version }} with numpy 1
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-latest
        python-version:
          - '3.10'
    steps:
      - uses: actions/checkout@v4
      - name: create build environment
        uses: mamba-org/setup-micromamba@v1
        with:
          environment-file: ./.tools/envs/testenv-numpy.yml
          cache-environment: true
          create-args: |
            python=${{ matrix.python-version }}
      - name: run pytest
        shell: bash -l {0}
        run: |
          micromamba activate optimagic
          pytest -m "not slow and not jax"
  code-in-docs:
    name: Run code snippets in documentation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: create build environment
        uses: mamba-org/setup-micromamba@v1
        with:
          environment-file: ./.tools/envs/testenv-linux.yml
          environment-name: optimagic
          cache-environment: true
          create-args: |
            python=3.12
      - name: run sphinx
        shell: bash -l {0}
        run: |-
          micromamba activate optimagic
          cd docs/source
          python -m doctest -v how_to/how_to_constraints.md
  run-mypy:
    name: Run mypy
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
    steps:
      - uses: actions/checkout@v4
      - name: create build environment
        uses: mamba-org/setup-micromamba@v1
        with:
          environment-file: ./.tools/envs/testenv-linux.yml
          environment-name: optimagic
          cache-environment: true
          create-args: |
            python=3.10
      - name: Run mypy
        shell: bash -l {0}
        run: |-
          micromamba activate optimagic
          mypy
