# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: tests

on:
  push:
    branches: [ "master" ]
  pull_request:

permissions:
  contents: read

jobs:
  build:
    name: Python ${{ matrix.python-version }} - numpy ${{ matrix.numpy-version }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12", "3.13"]
        numpy-version: [">=1.25,<2", ">=2"]
        exclude:
          - python-version: "3.13"
            numpy-version: ">=1.25,<2" # numpy<2 is not supported on Python 3.13

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install the latest version of uv
      uses: astral-sh/setup-uv@v5
      with:
        version: "latest"
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: 'latest'
    - name: Install test dependencies
      run: |
        poetry self add poetry-plugin-export
        poetry export -f requirements.txt --with dev,nbtools --without-hashes --output requirements-dev.txt
        echo "numpy${{ matrix.numpy-version }}" >> constraints.txt
        uv pip compile requirements-dev.txt --output-file requirements.txt \
          --python-version ${{ matrix.python-version }} \
          --override constraints.txt
        poetry run pip install -r requirements.txt
        poetry install --only-root
    - name: Test with pytest
      run: |
        poetry run pytest --cov-report xml --cov=bayes_opt/
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
          token: ${{ secrets.CODECOV_TOKEN }}
