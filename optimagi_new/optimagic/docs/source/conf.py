#!/usr/bin/env python3
#
# optimagic documentation build configuration file, created by
# sphinx-quickstart on Fri Jan 18 10:59:27 2019.
#
# This file is execfile()d with the current directory set to its
# containing dir.
#
# Note that not all possible configuration values are present in this
# autogenerated file.
#
# All configuration values have a default; values that are commented out
# serve to show the default.
# If extensions (or modules to document with autodoc) are in another directory,
# add these directories to sys.path here. If the directory is relative to the
# documentation root, use os.path.abspath to make it absolute, like shown here.
#
import datetime as dt
import os
from importlib.metadata import version

from intersphinx_registry import get_intersphinx_mapping

year = dt.datetime.now().year

author = "<PERSON><PERSON>"

# Set variable so that todos are shown in local build
on_rtd = os.environ.get("READTHEDOCS") == "True"


# -- General configuration ------------------------------------------------

# If your documentation needs a minimal Sphinx version, state it here.
#
# Add any Sphinx extension module names here, as strings. They can be
# extensions coming with <PERSON>phinx (named 'sphinx.ext.*') or your custom
# ones.
extensions = [
    "sphinx.ext.autodoc",
    "sphinx.ext.todo",
    "sphinx.ext.coverage",
    "sphinx.ext.extlinks",
    "sphinx.ext.intersphinx",
    "sphinx.ext.mathjax",
    "sphinx.ext.viewcode",
    "sphinx.ext.napoleon",
    "sphinx_copybutton",
    "myst_nb",
    "sphinxcontrib.bibtex",
    "sphinx_panels",
    "sphinx_design",
    "sphinxcontrib.mermaid",
]

myst_enable_extensions = [
    "colon_fence",
    "dollarmath",
    "html_image",
]
myst_fence_as_directive = ["mermaid"]


copybutton_prompt_text = ">>> "
copybutton_only_copy_prompt_lines = False

bibtex_bibfiles = ["refs.bib"]

autodoc_member_order = "bysource"

autodoc_mock_imports = [
    "bokeh",
    "cloudpickle",
    "cyipopt",
    "fides",
    "joblib",
    "nlopt",
    "pytest",
    "pygmo",
    "scipy",
    "sqlalchemy",
    "tornado",
    "petsc4py",
    "statsmodels",
    "numba",
]

extlinks = {
    "ghuser": ("https://github.com/%s", "@"),
    "gh": ("https://github.com/optimagic-dev/optimagic/pulls/%s", "#"),
}

intersphinx_mapping = get_intersphinx_mapping(
    packages={"numpy", "scipy", "pandas", "python"}
)

linkcheck_ignore = [
    r"https://tinyurl\.com/*.",
]

# Add any paths that contain templates here, relative to this directory.
templates_path = ["_templates"]

# The suffix(es) of source filenames.
# You can specify multiple suffix as a list of string:
#
source_suffix = [".rst", ".ipynb", ".md"]

# The master toctree document.
master_doc = "index"

# General information about the project.
project = "optimagic"
copyright = f"2019 - {year}, {author}"  # noqa: A001

# The version info for the project you're documenting, acts as replacement for
# |version| and |release|, also used in various other places throughout the
# built documents.
#
# The full version, including alpha/beta/rc tags.
release = version("optimagic").split("+")[0]
version = ".".join(release.split(".")[:2])

# The language for content autogenerated by Sphinx. Refer to documentation
# for a list of supported languages.
#
# This is also used if you do content translation via gettext catalogs.
# Usually you set "language" from the command line for these cases.
language = None

# List of patterns, relative to source directory, that match files and
# directories to ignore when looking for source files.
# This patterns also effect to html_static_path and html_extra_path
exclude_patterns = ["_build", "**.ipynb_checkpoints"]

# The name of the Pygments (syntax highlighting) style to use.
pygments_style = "sphinx"
pygments_dark_style = "monokai"

# If true, `todo` and `todoList` produce output, else they produce nothing.
if on_rtd:
    pass
else:
    todo_include_todos = True
    todo_emit_warnings = True

# -- Options for myst-nb  ----------------------------------------
nb_execution_mode = "force"
nb_execution_allow_errors = False
nb_merge_streams = True

# Notebook cell execution timeout; defaults to 30.
nb_execution_timeout = 1000

# List of notebooks that will not be executed.
nb_execution_excludepatterns = [
    # Problem with latex rendering
    "estimation_tables_overview.ipynb",
    # too long runtime
    "bootstrap_montecarlo_comparison.ipynb",
]

# -- Options for HTML output ----------------------------------------------

# The theme to use for HTML and HTML Help pages.  See the documentation for
# a list of builtin themes.
html_theme = "furo"

# Add any paths that contain custom static files (such as style sheets) here, relative
# to this directory. They are copied after the built-in static files, so a file named
# "default.css" will overwrite the built-in "default.css".
html_css_files = ["css/termynal.css", "css/termynal_custom.css", "css/custom.css"]

html_js_files = ["js/termynal.js", "js/custom.js"]


# Add any paths that contain custom static files (such as style sheets) here, relative
# to this directory. They are copied after the builtin static files, so a file named
# "default.css" will overwrite the builtin "default.css".
html_static_path = ["_static"]

# If false, no module index is generated.
html_domain_indices = True

# If false, no index is generated.
html_use_index = True

# If true, the index is split into individual pages for each letter.
html_split_index = False

# If true, links to the source (either copied by sphinx on on github)
html_copy_source = True

# If true, links to the reST sources are added to the pages.
html_show_sourcelink = True

# If true, "Created using Sphinx" is shown in the HTML footer. Default is True.
html_show_sphinx = True

# If true, "(C) Copyright ..." is shown in the HTML footer. Default is True.
html_show_copyright = True

html_title = "optimagic"

html_theme_options = {
    "sidebar_hide_name": True,
    "navigation_with_keys": True,
    "light_logo": "images/optimagic_logo.svg",
    "dark_logo": "images/optimagic_logo_dark_mode.svg",
    "light_css_variables": {
        "color-brand-primary": "#f04f43",
        "color-brand-content": "#f04f43",
    },
    "dark_css_variables": {
        "color-brand-primary": "#f04f43",
        "color-brand-content": "#f04f43",
    },
    "source_repository": "https://github.com/optimagic-dev/optimagic",
    "source_branch": "main",
    "source_directory": "docs/source/",
    "footer_icons": [
        {
            "name": "GitHub",
            "url": "https://github.com/optimagic-dev/optimagic",
            "html": """
                <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 ********** 1.21 1.87.87 **********-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 *********-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 **********.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 **********.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .***********.38A8.013 8.013 0 0 0 16 8c0-4.42-3.58-8-8-8z"></path>
                </svg>
            """,
            "class": "",
        },
        {
            "name": "Zulip",
            "url": "https://ose.zulipchat.com/#narrow/channel/221432-optimagic",
            "html": """
                <svg stroke="currentColor" fill="currentColor" stroke-width="0" role="img" viewBox="0 0 24 24" height="1em" width="1em"
                xmlns="http://www.w3.org/2000/svg">
                <path d="M22.767 3.589c0 1.209-.543 2.283-1.37 2.934l-8.034 7.174c-.149.128-.343-.078-.235-.25l2.946-5.9c.083-.165-.024-.368-.194-.368H4.452c-1.77 0-3.219-1.615-3.219-3.59C1.233 1.616 2.682 0 4.452 0h15.096c1.77-.001 3.219 1.614 3.219 3.589zM4.452 24h15.096c1.77 0 3.219-1.616 3.219-3.59 0-1.974-1.449-3.59-3.219-3.59H8.12c-.17 0-.277-.202-.194-.367l2.946-5.9c.108-.172-.086-.378-.235-.25l-8.033 7.173c-.828.65-1.37 1.725-1.37 2.934 0 1.974 1.448 3.59 3.218 3.59z"></path></svg>
            """,
            "class": "",
        },
    ],
}
