{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["(how-to-logging)=\n", "\n", "# How to use logging\n", "\n", "\n", "optimagic can keep a persistent log of the parameter and criterion values tried out by an optimizer in a sqlite database. \n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Turn logging on or off\n", "\n", "To enable logging, it suffices to provide a path to an sqlite database when calling ``maximize`` or ``minimize``. The database does not have to exist, optimagic will generate it for you. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "\n", "import numpy as np\n", "\n", "import optimagic as om"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sphere(params):\n", "    return params @ params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Remove the log file if it exists (just needed for the example)\n", "log_file = Path(\"my_log.db\")\n", "if log_file.exists():\n", "    log_file.unlink()\n", "\n", "res = om.minimize(\n", "    fun=sphere,\n", "    params=np.arange(5),\n", "    algorithm=\"scipy_lbfgsb\",\n", "    logging=\"my_log.db\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In case the SQLite file already exists, this will raise a `FileExistsError` to prevent from accidentally polluting an existing database. If you want to reuse\n", "an existing database on purpose, you must explicitly provide the corresponding option for `if_database_exists`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["log_options = om.SQLiteLogOptions(\n", "    \"my_log.db\", if_database_exists=om.ExistenceStrategy.EXTEND\n", ")\n", "\n", "res = om.minimize(\n", "    fun=sphere,\n", "    params=np.arange(5),\n", "    algorithm=\"scipy_lbfgsb\",\n", "    logging=log_options,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Make logging faster\n", "\n", "By default, we use a very safe mode of sqlite that makes it almost impossible to corrupt the database. Even if your computer is suddenly shut down or unplugged. \n", "\n", "However, this makes writing logs rather slow, which becomes notable when the criterion function is very fast. \n", "\n", "In that case, you can enable `fast_logging`, which is still quite safe!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["log_options = om.SQLiteLogOptions(\n", "    \"my_log.db\",\n", "    fast_logging=True,\n", "    if_database_exists=om.ExistenceStrategy.REPLACE,\n", ")\n", "\n", "res = om.minimize(\n", "    fun=sphere,\n", "    params=np.arange(5),\n", "    algorithm=\"scipy_lbfgsb\",\n", "    logging=log_options,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Reading the log\n", "To read the log after an optimization, extract the logger from the optimization result:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reader = res.logger"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alternatively, you can create the reader like this:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reader = om.SQLiteLogReader(\"my_log.db\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Read the start params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reader.read_start_params()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Read a specific iteration (use -1 for the last)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reader.read_iteration(-1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Read the full history"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["reader.read_history().keys()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot the history from a log"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig = om.criterion_plot(\"my_log.db\")\n", "fig.show(renderer=\"png\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fig = om.params_plot(\"my_log.db\", selector=lambda x: x[1:3])\n", "fig.show(renderer=\"png\")"]}], "metadata": {"interpreter": {"hash": "5cdb9867252288f10687117449de6ad870b49795ca695c868016dc0022895cce"}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}