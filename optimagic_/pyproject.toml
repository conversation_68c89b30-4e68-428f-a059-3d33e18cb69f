# ======================================================================================
# Project metadata
# ======================================================================================
[project]
name = "optimagic"
description = "Tools to solve difficult numerical optimization problems."
requires-python = ">=3.10"
dependencies = [
    "cloudpickle",
    "joblib",
    "numpy",
    "pandas",
    "plotly<6.0.0",
    "pybaum>=0.1.2",
    "scipy>=1.2.1",
    "sqlalchemy>=1.3",
    "annotated-types",
    "typing-extensions",
    "iminuit",
]
dynamic = ["version"]
keywords = [
    "nonlinear optimization",
    "optimization",
    "derivative free optimization",
    "global optimization",
    "parallel optimization",
    "statistics",
    "estimation",
    "extremum estimation",
    "inference",
    "numerical differentiation",
    "finite differences",
    "method of simulated moments",
    "maximum likelihood",
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: MacOS :: MacOS X",
    "Operating System :: Microsoft :: Windows",
    "Operating System :: POSIX",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Scientific/Engineering",
]
authors = [
    { name = "Janos Gabler", email = "<EMAIL>" },
]
maintainers = [
    { name = "Janos Gabler", email = "<EMAIL>" },
    { name = "Tim Mensinger", email = "<EMAIL>" },
]

[project.readme]
file = "README.md"
content-type = "text/markdown"

[project.license]
text = "MIT"

[project.urls]
Repository = "https://github.com/optimagic-dev/optimagic"
Github = "https://github.com/optimagic-dev/optimagic"
Tracker = "https://github.com/optimagic-dev/optimagic/issues"


# ======================================================================================
# Build system configuration
# ======================================================================================
[build-system]
requires = ["hatchling", "hatch_vcs"]
build-backend = "hatchling.build"

[tool.hatch.build.hooks.vcs]
version-file = "src/optimagic/_version.py"

[tool.hatch.build.targets.sdist]
exclude = ["tests"]
only-packages = true

[tool.hatch.build.targets.wheel]
only-include = ["src"]
sources = ["src"]

[tool.hatch.version]
source = "vcs"

[tool.hatch.metadata]
allow-direct-references = true


# ======================================================================================
# Ruff configuration
# ======================================================================================
[tool.ruff]
target-version = "py310"
fix = true

[tool.ruff.lint]
select = [
  # isort
  "I",
  # pyflakes
  "F",
  # pycodestyle
  "E",
  "W",
  # flake8-2020
  "YTT",
  # flake8-bugbear
  "B",
  # flake8-quotes
  "Q",
  # pylint
  "PLE", "PLR", "PLW",
  # misc lints
  "PIE",
  # tidy imports
  "TID",
  # implicit string concatenation
  "ISC",
]

extend-ignore = [

  # allow module import not at top of file, important for notebooks
  "E402",
  # do not assign a lambda expression, use a def
  "E731",
  # Too many arguments to function call
  "PLR0913",
  # Too many returns
  "PLR0911",
  # Too many branches
  "PLR0912",
  # Too many statements
  "PLR0915",
  # Magic number
  "PLR2004",
  # Consider `elif` instead of `else` then `if` to remove indentation level
  "PLR5501",
  # For calls to warnings.warn(): No explicit `stacklevel` keyword argument found
  "B028",
  # Incompatible with formatting
  "ISC001",
]

[tool.ruff.lint.per-file-ignores]
"docs/source/conf.py" = ["E501", "ERA001", "DTZ005"]
"src/optimagic/parameters/kernel_transformations.py" = ["ARG001", "N806"]
"docs/source/*" = ["B018"]
"src/optimagic/algorithms.py" = ["E501"]

[tool.ruff.lint.pydocstyle]
convention = "google"


# ======================================================================================
# Pytest configuration
# ======================================================================================
[tool.pytest.ini_options]
filterwarnings = [
    "ignore:Using or importing the ABCs from 'collections'",
    "ignore:the imp module is deprecated",
    "ignore:Method .ptp is deprecated and will be removed in a future version. Use numpy.ptp instead.",
    "ignore:In a future version of pandas all arguments of concat except for the argument 'objs' will be keyword-only",
    "ignore:Please use `MemoizeJac` from the `scipy.optimize` namespace",
    "ignore:`scipy.optimize.optimize.MemoizeJac` is deprecated",
    "ignore:Some algorithms did not converge. Their walltime has been set to a very high value instead of infinity because Timedeltas do notsupport infinite values",
    "ignore:In a future version, the Index constructor will not infer numeric dtypes when passed object-dtype sequences",
    "ignore:distutils Version classes are deprecated. Use packaging.version instead",
    "ignore:Standard matrix inversion failed due to LinAlgError",
    "ignore:delta_grad == 0.0",
    "ignore:Widget._active_widgets is deprecated",
    "ignore:Widget._widget_types is deprecated",
    "ignore:Widget.widget_types is deprecated",
    "ignore:Widget.widgets is deprecated",
    "ignore:Parallelization together with",
    "ignore:Conversion of an array with ndim > 0 to a scalar is deprecated",
    "ignore:The following exception was caught when evaluating",
    "ignore:The following exception was caught when calculating",
    "ignore:Usage of the parameter log_options",
]
addopts = ["--doctest-modules", "--pdbcls=pdbp:Pdb"]
markers = [
    "wip: Tests that are work-in-progress.",
    "slow: Tests that take a long time to run and are skipped in continuous integration.",
    "jax: Tests that require jax to be installed and are skipped on non-Linux systems.",
]
norecursedirs = ["docs", ".tools"]


# ======================================================================================
# Misc configuration
# ======================================================================================
[tool.yamlfix]
line_length = 88
sequence_style = "block_style"
none_representation = "null"


# ======================================================================================
# Mypy configuration
# ======================================================================================
[tool.mypy]
files = ["src", "tests", ".tools"]
check_untyped_defs = true
disallow_any_generics = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true

[[tool.mypy.overrides]]
module = [
    "optimagic.benchmarking",
    "optimagic.benchmarking.benchmark_reports",
    "optimagic.benchmarking.cartis_roberts",
    "optimagic.benchmarking.get_benchmark_problems",
    "optimagic.benchmarking.more_wild",
    "optimagic.benchmarking.noise_distributions",
    "optimagic.benchmarking.process_benchmark_results",
    "optimagic.benchmarking.run_benchmark",

    "optimagic.differentiation",
    "optimagic.differentiation.derivatives",
    "optimagic.differentiation.finite_differences",
    "optimagic.differentiation.generate_steps",
    "optimagic.differentiation.richardson_extrapolation",

    "optimagic.examples",
    "optimagic.examples.numdiff_functions",

    "optimagic.optimization",
    "optimagic.optimization.algo_options",
    "optimagic.optimization.convergence_report",
    "optimagic.optimization.optimization_logging",
    "optimagic.optimization.optimize_result",
    "optimagic.optimization.optimize",
    "optimagic.optimization.multistart",
    "optimagic.optimization.scipy_aliases",
    "optimagic.optimization.create_optimization_problem",

    "optimagic.optimizers._pounders",
    "optimagic.optimizers._pounders.pounders_auxiliary",
    "optimagic.optimizers._pounders.pounders_history",
    "optimagic.optimizers._pounders._conjugate_gradient",
    "optimagic.optimizers._pounders._steihaug_toint",
    "optimagic.optimizers._pounders._trsbox",
    "optimagic.optimizers._pounders.bntr",
    "optimagic.optimizers._pounders.gqtpar",
    "optimagic.optimizers._pounders.linear_subsolvers",

    "optimagic.optimizers",
    "optimagic.optimizers.tranquilo",
    "optimagic.optimizers.pygmo_optimizers",
    "optimagic.optimizers.scipy_optimizers",
    "optimagic.optimizers.nag_optimizers",
    "optimagic.optimizers.neldermead",
    "optimagic.optimizers.nlopt_optimizers",
    "optimagic.optimizers.ipopt",
    "optimagic.optimizers.fides",
    "optimagic.optimizers.pounders",
    "optimagic.optimizers.tao_optimizers",


    "optimagic.parameters",
    "optimagic.parameters.block_trees",
    "optimagic.parameters.check_constraints",
    "optimagic.parameters.consolidate_constraints",
    "optimagic.parameters.constraint_tools",
    "optimagic.parameters.conversion",
    "optimagic.parameters.kernel_transformations",
    "optimagic.parameters.nonlinear_constraints",
    "optimagic.parameters.process_constraints",
    "optimagic.parameters.process_selectors",
    "optimagic.parameters.space_conversion",
    "optimagic.parameters.tree_conversion",
    "optimagic.parameters.tree_registry",


    "optimagic.shared",
    "optimagic.shared.check_option_dicts",
    "optimagic.shared.compat",
    "optimagic.shared.process_user_function",

    "optimagic.visualization",
    "optimagic.visualization.convergence_plot",
    "optimagic.visualization.deviation_plot",
    "optimagic.visualization.history_plots",
    "optimagic.visualization.plotting_utilities",
    "optimagic.visualization.profile_plot",
    "optimagic.visualization.slice_plot",

    "optimagic",
    "optimagic.decorators",
    "optimagic.exceptions",
    "optimagic.utilities",
    "optimagic.deprecations",

    "estimagic",
    "estimagic.examples",
    "estimagic.examples.logit",
    "estimagic.estimate_ml",
    "estimagic.estimate_msm",
    "estimagic.estimation_summaries",
    "estimagic.msm_weighting",
    "estimagic.bootstrap_ci",
    "estimagic.bootstrap_helpers",
    "estimagic.bootstrap_outcomes",
    "estimagic.bootstrap_samples",
    "estimagic.bootstrap",
    "estimagic.ml_covs",
    "estimagic.msm_covs",
    "estimagic.shared_covs",
    "estimagic.msm_sensitivity",
    "estimagic.estimation_table",
    "estimagic.lollipop_plot",

]
check_untyped_defs = false
disallow_any_generics = false
disallow_untyped_defs = false


[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
ignore_errors = true

[[tool.mypy.overrides]]
module = [
    "pybaum",
    "scipy",
    "scipy.linalg",
    "scipy.linalg.lapack",
    "scipy.stats",
    "scipy.optimize",
    "scipy.ndimage",
    "scipy.optimize._trustregion_exact",
    "plotly",
    "plotly.graph_objects",
    "plotly.express",
    "plotly.subplots",
    "cyipopt",
    "nlopt",
    "bokeh",
    "bokeh.layouts",
    "bokeh.models",
    "bokeh.plotting",
    "bokeh.application",
    "bokeh.application.handlers",
    "bokeh.application.handlers.function",
    "bokeh.server",
    "bokeh.server.server",
    "bokeh.command",
    "bokeh.command.util",
    "fides",
    "petsc4py",
    "petsc4py.PETSc",
    "tranquilo",
    "tranquilo.tranquilo",
    "tranquilo.options",
    "tranquilo.process_arguments",
    "dfols",
    "pybobyqa",
    "pygmo",
    "jax",
    "joblib",
    "cloudpickle",
    "numba",
    "pathos",
    "pathos.pools",
    "optimagic._version",
    "annotated_types",
    "pdbp",
    "iminuit",
    "nevergrad",
    "bayes_opt",
    "bayes_opt.acquisition"
  ]
ignore_missing_imports = true
